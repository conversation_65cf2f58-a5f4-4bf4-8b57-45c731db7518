@echo off
chcp 65001 >nul
title 露天矿山道路设计软件 - 系统诊断

:: 获取脚本所在目录
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

echo.
echo ========================================
echo   露天矿山道路设计软件系统诊断 v1.0
echo ========================================
echo.

echo 🔍 开始系统诊断...
echo 📁 项目目录: %SCRIPT_DIR%
echo.

:: 创建诊断报告文件
set REPORT_FILE=诊断报告_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
set REPORT_FILE=%REPORT_FILE: =0%

echo 生成诊断报告: %REPORT_FILE%
echo ======================================== > %REPORT_FILE%
echo 露天矿山道路设计软件系统诊断报告 >> %REPORT_FILE%
echo 生成时间: %date% %time% >> %REPORT_FILE%
echo 项目目录: %SCRIPT_DIR% >> %REPORT_FILE%
echo ======================================== >> %REPORT_FILE%
echo. >> %REPORT_FILE%

:: 1. 系统信息
echo [1/8] 收集系统信息...
echo 1. 系统信息 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
systeminfo | findstr /C:"OS Name" /C:"OS Version" /C:"System Type" /C:"Total Physical Memory" >> %REPORT_FILE%
echo. >> %REPORT_FILE%

:: 2. Node.js环境
echo [2/8] 检查Node.js环境...
echo 2. Node.js环境 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
node --version >> %REPORT_FILE% 2>&1
npm --version >> %REPORT_FILE% 2>&1
echo Node.js安装路径: >> %REPORT_FILE%
where node >> %REPORT_FILE% 2>&1
echo npm安装路径: >> %REPORT_FILE%
where npm >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

:: 3. 项目信息
echo [3/8] 检查项目信息...
echo 3. 项目信息 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
if exist "package.json" (
    echo ✅ package.json存在 >> %REPORT_FILE%
    findstr /C:"name" /C:"version" /C:"scripts" package.json >> %REPORT_FILE%
) else (
    echo ❌ package.json不存在 >> %REPORT_FILE%
)

if exist "src" (
    echo ✅ src目录存在 >> %REPORT_FILE%
) else (
    echo ❌ src目录不存在 >> %REPORT_FILE%
)

if exist "vite.config.ts" (
    echo ✅ vite.config.ts存在 >> %REPORT_FILE%
) else (
    echo ❌ vite.config.ts不存在 >> %REPORT_FILE%
)
echo. >> %REPORT_FILE%

:: 4. 依赖检查
echo [4/8] 检查项目依赖...
echo 4. 项目依赖 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
if exist "node_modules" (
    echo ✅ node_modules目录存在 >> %REPORT_FILE%
    for /f %%i in ('dir /ad node_modules 2^>nul ^| find /c "个目录"') do echo 依赖包数量: %%i >> %REPORT_FILE%
) else (
    echo ❌ node_modules目录不存在 >> %REPORT_FILE%
)

if exist "package-lock.json" (
    echo ✅ package-lock.json存在 >> %REPORT_FILE%
) else (
    echo ❌ package-lock.json不存在 >> %REPORT_FILE%
)
echo. >> %REPORT_FILE%

:: 5. 端口检查
echo [5/8] 检查端口占用...
echo 5. 端口占用情况 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
echo 检查端口3000: >> %REPORT_FILE%
netstat -an | findstr ":3000" >> %REPORT_FILE%
echo 检查端口24678: >> %REPORT_FILE%
netstat -an | findstr ":24678" >> %REPORT_FILE%
echo. >> %REPORT_FILE%

:: 6. 网络连接
echo [6/8] 检查网络连接...
echo 6. 网络连接 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
echo 测试百度连接: >> %REPORT_FILE%
ping -n 1 www.baidu.com >> %REPORT_FILE% 2>&1
echo 测试npm镜像连接: >> %REPORT_FILE%
ping -n 1 registry.npmjs.org >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

:: 7. 磁盘空间
echo [7/8] 检查磁盘空间...
echo 7. 磁盘空间 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
for %%i in (C: D: E: F:) do (
    if exist %%i\ (
        for /f "tokens=3" %%j in ('dir %%i\ ^| find "可用字节"') do echo %%i 可用空间: %%j 字节 >> %REPORT_FILE%
    )
)
echo. >> %REPORT_FILE%

:: 8. 环境变量
echo [8/8] 检查环境变量...
echo 8. 相关环境变量 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
echo NODE_ENV: %NODE_ENV% >> %REPORT_FILE%
echo TEMP: %TEMP% >> %REPORT_FILE%
echo PATH中的Node.js路径: >> %REPORT_FILE%
echo %PATH% | findstr /i node >> %REPORT_FILE%
echo. >> %REPORT_FILE%

:: 诊断建议
echo 9. 诊断建议 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%

:: 检查Node.js版本
for /f "tokens=1 delims=v" %%i in ('node --version 2^>nul') do set NODE_VER=%%i
if "%NODE_VER%" LSS "16" (
    echo ⚠️  建议升级Node.js到16.0以上版本 >> %REPORT_FILE%
)

:: 检查依赖
if not exist "node_modules" (
    echo ⚠️  需要安装项目依赖: npm install >> %REPORT_FILE%
)

:: 检查磁盘空间
for /f "tokens=3" %%i in ('dir E:\ 2^>nul ^| find "可用字节"') do (
    if %%i LSS 1073741824 echo ⚠️  E盘空间不足，建议至少保留1GB空间 >> %REPORT_FILE%
)

echo ✅ 诊断完成，请查看详细报告: %REPORT_FILE% >> %REPORT_FILE%

echo.
echo ✅ 系统诊断完成！
echo.
echo 📋 诊断报告已保存到: %REPORT_FILE%
echo.
echo 📊 诊断摘要:
echo    - 系统信息: 已收集
echo    - Node.js环境: 已检查
echo    - 项目依赖: 已验证
echo    - 网络连接: 已测试
echo    - 磁盘空间: 已检查
echo.

:: 询问是否打开报告
set /p OPEN_REPORT=是否打开诊断报告？(y/n): 
if /i "%OPEN_REPORT%"=="y" (
    start notepad %REPORT_FILE%
)

echo.
echo 💡 如果遇到问题，请将诊断报告发送给技术支持
echo.
pause
