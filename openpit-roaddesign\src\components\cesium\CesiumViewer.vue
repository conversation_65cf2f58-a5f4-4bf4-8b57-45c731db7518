<template>
  <div class="cesium-viewer-container">
    <!-- Cesium 3D地球容器 -->
    <div class="cesium-container" ref="cesiumContainer">
      <!-- 加载指示器 -->
      <div v-if="loading" class="loading-overlay">
        <el-loading-spinner />
        <p>{{ loadingText || '加载中...' }}</p>
      </div>
    </div>

    <!-- 右侧悬浮工具栏 -->
    <div class="floating-toolbar">
      <!-- 视图控制 -->
      <div class="toolbar-group">
        <el-tooltip content="放大" placement="left">
          <el-button circle size="small" @click="zoomIn">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="缩小" placement="left">
          <el-button circle size="small" @click="zoomOut">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="回到初始位置" placement="left">
          <el-button circle size="small" @click="goHome">
            <el-icon><HomeFilled /></el-icon>
          </el-button>
        </el-tooltip>
      </div>

      <!-- 图层控制 -->
      <div class="toolbar-group">
        <el-tooltip content="地形开关" placement="left">
          <el-button 
            circle 
            size="small" 
            :type="terrainEnabled ? 'primary' : 'default'"
            @click="toggleTerrain"
          >
            <el-icon><Mountain /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="2D/3D切换" placement="left">
          <el-button 
            circle 
            size="small" 
            :type="is3D ? 'primary' : 'default'"
            @click="toggle2D3D"
          >
            <el-icon><View /></el-icon>
          </el-button>
        </el-tooltip>
      </div>

      <!-- 分析工具 -->
      <div class="toolbar-group">
        <el-tooltip content="距离测量" placement="left">
          <el-button 
            circle 
            size="small" 
            :type="measureMode === 'distance' ? 'primary' : 'default'"
            @click="toggleMeasureDistance"
          >
            <el-icon><Ruler /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="面积测量" placement="left">
          <el-button 
            circle 
            size="small" 
            :type="measureMode === 'area' ? 'primary' : 'default'"
            @click="toggleMeasureArea"
          >
            <el-icon><Crop /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="高程查询" placement="left">
          <el-button 
            circle 
            size="small" 
            :type="measureMode === 'elevation' ? 'primary' : 'default'"
            @click="toggleElevationQuery"
          >
            <el-icon><TrendCharts /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span class="status-item">坐标: {{ currentCoordinates }}</span>
        <span class="status-item">高程: {{ currentElevation }}m</span>
        <span class="status-item">比例尺: 1:{{ currentScale }}</span>
      </div>
      <div class="status-right">
        <span class="status-item">视图: {{ is3D ? '3D' : '2D' }}</span>
        <span class="status-item">FPS: {{ renderFPS }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useGISStore } from '@/stores/gis'
import { 
  ZoomIn,
  ZoomOut,
  HomeFilled,
  Mountain,
  View,
  Ruler,
  Crop,
  TrendCharts
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as Cesium from 'cesium'
import { CESIUM_CONFIG } from '@/config'

interface Props {
  initialPosition?: {
    longitude: number
    latitude: number
    height: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  initialPosition: () => ({ longitude: 116.3974, latitude: 39.9093, height: 1000 })
})

const emit = defineEmits<{
  'viewer-ready': [viewer: Cesium.Viewer]
  'position-changed': [position: { longitude: number, latitude: number, elevation: number }]
  'data-clicked': [data: any]
}>()

const gisStore = useGISStore()

// 响应式数据
const cesiumContainer = ref<HTMLElement>()
const loading = ref(true)
const loadingText = ref('初始化Cesium...')
const terrainEnabled = ref(true)
const is3D = ref(true)
const measureMode = ref<'distance' | 'area' | 'elevation' | null>(null)

// 状态栏数据
const currentCoordinates = ref('116.3974°, 39.9093°')
const currentElevation = ref(50)
const currentScale = ref(100000)
const renderFPS = ref(60)

// Cesium viewer实例
let viewer: Cesium.Viewer | null = null
let measureHandler: Cesium.ScreenSpaceEventHandler | null = null

onMounted(async () => {
  await initCesium()
})

onUnmounted(() => {
  cleanup()
})

// 监听GIS数据变化
watch(() => gisStore.allData, (newData) => {
  updateDataLayers()
}, { deep: true })

// 初始化Cesium
async function initCesium() {
  try {
    loadingText.value = '创建Cesium视图...'
    
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到')
    }

    // 创建Cesium viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      ...CESIUM_CONFIG.viewer,
      terrainProvider: await Cesium.createWorldTerrainAsync({
        requestVertexNormals: true,
        requestWaterMask: true
      })
    })

    // 设置初始视图
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(
        props.initialPosition.longitude, 
        props.initialPosition.latitude, 
        props.initialPosition.height
      ),
      orientation: {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      }
    })

    // 添加事件监听
    setupEventListeners()

    // 初始化数据图层
    updateDataLayers()

    loadingText.value = '加载完成'
    loading.value = false
    
    emit('viewer-ready', viewer)
    ElMessage.success('Cesium初始化成功')
  } catch (error) {
    console.error('Cesium初始化失败:', error)
    ElMessage.error('Cesium初始化失败')
    loading.value = false
  }
}

// 设置事件监听器
function setupEventListeners() {
  if (!viewer) return

  // 鼠标移动事件
  viewer.canvas.addEventListener('mousemove', updateMousePosition)

  // 相机移动事件
  viewer.camera.moveEnd.addEventListener(updateCameraInfo)

  // 点击事件
  viewer.cesiumWidget.screenSpaceEventHandler.setInputAction((event: any) => {
    const pickedObject = viewer!.scene.pick(event.position)
    if (pickedObject) {
      emit('data-clicked', pickedObject)
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
}

// 更新鼠标位置信息
function updateMousePosition(event: MouseEvent) {
  if (!viewer) return

  const pick = viewer.camera.getPickRay(new Cesium.Cartesian2(event.clientX, event.clientY))
  if (!pick) return

  const globe = viewer.scene.globe
  const cartesian = globe.pick(pick, viewer.scene)
  if (!cartesian) return

  const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
  const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6)
  const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6)
  const height = cartographic.height.toFixed(1)

  currentCoordinates.value = `${longitude}°, ${latitude}°`
  currentElevation.value = parseFloat(height)

  emit('position-changed', {
    longitude: parseFloat(longitude),
    latitude: parseFloat(latitude),
    elevation: parseFloat(height)
  })
}

// 更新相机信息
function updateCameraInfo() {
  if (!viewer) return

  const camera = viewer.camera
  const height = camera.positionCartographic.height
  currentScale.value = Math.round(height / 10)
}

// 更新数据图层
function updateDataLayers() {
  if (!viewer) return

  // 清除现有实体
  viewer.entities.removeAll()

  // 添加GIS数据
  gisStore.allData.forEach(data => {
    if (data.visible) {
      addDataToViewer(data)
    }
  })
}

// 添加数据到视图
function addDataToViewer(data: any) {
  if (!viewer) return

  // 这里根据数据类型添加不同的可视化
  switch (data.type) {
    case 'drilling':
      addDrillingData(data)
      break
    case 'geology':
      addGeologyData(data)
      break
    case 'road':
      addRoadData(data)
      break
    default:
      console.log('未支持的数据类型:', data.type)
  }
}

// 添加钻孔数据
function addDrillingData(data: any) {
  if (!viewer) return

  // 模拟钻孔位置
  const position = Cesium.Cartesian3.fromDegrees(
    116.3974 + Math.random() * 0.01,
    39.9093 + Math.random() * 0.01,
    0
  )

  viewer.entities.add({
    position: position,
    cylinder: {
      length: 100,
      topRadius: 5,
      bottomRadius: 5,
      material: Cesium.Color.YELLOW.withAlpha(0.8)
    },
    label: {
      text: data.name,
      font: '12pt sans-serif',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      pixelOffset: new Cesium.Cartesian2(0, -50)
    }
  })
}

// 添加地质数据
function addGeologyData(data: any) {
  if (!viewer) return

  // 模拟地质区域
  const positions = []
  for (let i = 0; i < 4; i++) {
    positions.push(
      116.3974 + Math.random() * 0.005,
      39.9093 + Math.random() * 0.005
    )
  }

  viewer.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray(positions),
      material: Cesium.Color.fromCssColorString(data.color || '#ff6b6b').withAlpha(0.6),
      outline: true,
      outlineColor: Cesium.Color.fromCssColorString(data.color || '#ff6b6b')
    },
    label: {
      text: data.name,
      font: '12pt sans-serif',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE
    }
  })
}

// 添加道路数据
function addRoadData(data: any) {
  if (!viewer) return

  // 模拟道路路径
  const positions = []
  for (let i = 0; i < 10; i++) {
    positions.push(
      116.3974 + i * 0.001,
      39.9093 + Math.sin(i * 0.5) * 0.001,
      50
    )
  }

  viewer.entities.add({
    polyline: {
      positions: Cesium.Cartesian3.fromDegreesArrayHeights(positions),
      width: 10,
      material: Cesium.Color.ORANGE,
      clampToGround: true
    },
    label: {
      text: data.name,
      font: '12pt sans-serif',
      fillColor: Cesium.Color.WHITE,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 2,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE
    }
  })
}

// 工具栏方法
function zoomIn() {
  if (!viewer) return
  viewer.camera.zoomIn(viewer.camera.positionCartographic.height * 0.5)
}

function zoomOut() {
  if (!viewer) return
  viewer.camera.zoomOut(viewer.camera.positionCartographic.height * 0.5)
}

function goHome() {
  if (!viewer) return
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      props.initialPosition.longitude, 
      props.initialPosition.latitude, 
      props.initialPosition.height
    )
  })
}

function toggleTerrain() {
  terrainEnabled.value = !terrainEnabled.value
  if (viewer) {
    viewer.terrainProvider = terrainEnabled.value 
      ? Cesium.createWorldTerrain()
      : new Cesium.EllipsoidTerrainProvider()
  }
}

function toggle2D3D() {
  if (!viewer) return
  
  is3D.value = !is3D.value
  viewer.scene.mode = is3D.value 
    ? Cesium.SceneMode.SCENE3D 
    : Cesium.SceneMode.SCENE2D
}

function toggleMeasureDistance() {
  measureMode.value = measureMode.value === 'distance' ? null : 'distance'
  setupMeasureTool()
}

function toggleMeasureArea() {
  measureMode.value = measureMode.value === 'area' ? null : 'area'
  setupMeasureTool()
}

function toggleElevationQuery() {
  measureMode.value = measureMode.value === 'elevation' ? null : 'elevation'
  setupMeasureTool()
}

function setupMeasureTool() {
  if (measureHandler) {
    measureHandler.destroy()
    measureHandler = null
  }

  if (!measureMode.value || !viewer) return

  measureHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)
  
  switch (measureMode.value) {
    case 'distance':
      setupDistanceMeasure()
      break
    case 'area':
      setupAreaMeasure()
      break
    case 'elevation':
      setupElevationQuery()
      break
  }
}

function setupDistanceMeasure() {
  ElMessage.info('点击两个点测量距离')
  // 距离测量实现
}

function setupAreaMeasure() {
  ElMessage.info('点击多个点测量面积，双击结束')
  // 面积测量实现
}

function setupElevationQuery() {
  ElMessage.info('点击地面查询高程')
  // 高程查询实现
}

function cleanup() {
  if (measureHandler) {
    measureHandler.destroy()
    measureHandler = null
  }
  
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
}

// 暴露方法给父组件
defineExpose({
  viewer,
  zoomIn,
  zoomOut,
  goHome,
  toggleTerrain,
  toggle2D3D
})
</script>

<style scoped>
.cesium-viewer-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.cesium-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-text);
  z-index: 1000;
}

.loading-overlay p {
  margin-top: var(--spacing-md);
  font-size: var(--font-size-lg);
}

.floating-toolbar {
  position: absolute;
  right: var(--spacing-md);
  top: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  z-index: 100;
}

.toolbar-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-xs);
  box-shadow: var(--shadow-2);
}

.toolbar-group .el-button {
  background-color: transparent;
  border-color: var(--color-border);
  color: var(--color-text-soft);
}

.toolbar-group .el-button:hover {
  background-color: var(--color-background-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.toolbar-group .el-button.el-button--primary {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: #000000;
}

.status-bar {
  height: 24px;
  background-color: var(--color-background-mute);
  border-top: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

.status-left,
.status-right {
  display: flex;
  gap: var(--spacing-md);
}

.status-item {
  white-space: nowrap;
}

/* Cesium样式覆盖 */
:deep(.cesium-viewer-bottom) {
  display: none;
}

:deep(.cesium-widget-credits) {
  display: none;
}

:deep(.cesium-viewer-toolbar) {
  display: none;
}
</style>
