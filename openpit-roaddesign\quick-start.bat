@echo off
chcp 65001 >nul
title 露天矿山道路设计软件 - 快速启动

:: 获取脚本所在目录并切换到项目目录
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

echo 🚀 启动露天矿山道路设计软件...
echo 📁 项目目录: %SCRIPT_DIR%
echo.

:: 检查Node.js
echo 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

:: 检查npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm不可用
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: %NPM_VERSION%

:: 检查项目文件
if not exist "package.json" (
    echo ❌ 未找到package.json文件
    echo    请确保在项目根目录运行此脚本
    pause
    exit /b 1
)
echo ✅ 项目文件检查通过

:: 检查依赖
if not exist "node_modules" (
    echo 📦 安装依赖中...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

:: 启动开发服务器
echo.
echo 🌐 启动开发服务器...
echo    访问地址: http://localhost:3000
echo    按 Ctrl+C 停止服务器
echo.
npm run dev

echo.
echo ⚠️  开发服务器已停止
pause
