/**
 * 道路安全检测算法
 * 实现坡度检测、转弯半径检测、视距检测、道路宽度检测等安全评估功能
 */

import type { 
  RoadData, 
  SafetyCheckResult, 
  RoadDesignStandards,
  Point3D 
} from '@/types'
import { OPENPIT_ROAD_STANDARDS } from '@/config'

// 安全检测配置接口
export interface SafetyCheckConfig {
  roadType: string
  strictness: 'strict' | 'normal' | 'loose'
  checkItems: string[]
  customStandards?: Partial<RoadDesignStandards>
}

// 检测结果统计
export interface SafetyCheckSummary {
  totalChecks: number
  passCount: number
  warningCount: number
  failCount: number
  safetyScore: number
  criticalIssues: SafetyCheckResult[]
}

/**
 * 道路安全检测主类
 */
export class RoadSafetyChecker {
  private standards: RoadDesignStandards
  private config: SafetyCheckConfig

  constructor(config: SafetyCheckConfig) {
    this.config = config
    this.standards = {
      ...OPENPIT_ROAD_STANDARDS[config.roadType],
      ...config.customStandards
    }
  }

  /**
   * 执行完整的安全检测
   */
  public async performSafetyCheck(roadData: RoadData): Promise<SafetyCheckResult[]> {
    const results: SafetyCheckResult[] = []

    // 根据配置执行不同的检测项目
    for (const checkItem of this.config.checkItems) {
      switch (checkItem) {
        case 'grade':
          results.push(...this.checkGrade(roadData))
          break
        case 'radius':
          results.push(...this.checkRadius(roadData))
          break
        case 'sight_distance':
          results.push(...this.checkSightDistance(roadData))
          break
        case 'width':
          results.push(...this.checkWidth(roadData))
          break
        case 'clearance':
          results.push(...this.checkClearance(roadData))
          break
        case 'drainage':
          results.push(...this.checkDrainage(roadData))
          break
        case 'stability':
          results.push(...this.checkStability(roadData))
          break
        case 'intersection':
          results.push(...this.checkIntersections(roadData))
          break
      }
    }

    return results
  }

  /**
   * 坡度检测
   */
  private checkGrade(roadData: RoadData): SafetyCheckResult[] {
    const results: SafetyCheckResult[] = []
    const points = roadData.centerline.points

    for (let i = 1; i < points.length; i++) {
      const prevPoint = points[i - 1]
      const currPoint = points[i]
      
      const horizontalDistance = this.calculateHorizontalDistance(prevPoint, currPoint)
      const verticalDistance = currPoint.elevation - prevPoint.elevation
      const grade = Math.abs(verticalDistance / horizontalDistance) * 100

      let status: 'pass' | 'warning' | 'fail' = 'pass'
      let message = `纵坡 ${grade.toFixed(1)}% 符合要求`

      // 根据严格程度调整标准
      const maxGrade = this.getAdjustedStandard('maxGrade', this.standards.maxGrade)

      if (grade > maxGrade) {
        status = grade > maxGrade * 1.2 ? 'fail' : 'warning'
        message = `纵坡 ${grade.toFixed(1)}% 超出标准值 ${maxGrade}%`
      }

      // 检查连续长坡
      if (grade > maxGrade * 0.8) {
        const longSlopeLength = this.checkLongSlope(points, i, maxGrade * 0.8)
        if (longSlopeLength > 500) { // 连续长坡超过500米
          status = 'warning'
          message += `，且存在 ${longSlopeLength.toFixed(0)}m 连续长坡`
        }
      }

      if (status !== 'pass') {
        results.push({
          type: 'grade',
          status,
          value: grade,
          standard: maxGrade,
          message,
          location: {
            station: currPoint.station,
            coordinates: [currPoint.longitude, currPoint.latitude, currPoint.elevation]
          }
        })
      }
    }

    return results
  }

  /**
   * 转弯半径检测
   */
  private checkRadius(roadData: RoadData): SafetyCheckResult[] {
    const results: SafetyCheckResult[] = []
    const curves = roadData.centerline.horizontalCurves

    for (const curve of curves) {
      let status: 'pass' | 'warning' | 'fail' = 'pass'
      let message = `转弯半径 ${curve.radius.toFixed(0)}m 符合要求`

      const minRadius = this.getAdjustedStandard('minRadius', this.standards.minRadius)

      if (curve.radius < minRadius) {
        status = curve.radius < minRadius * 0.8 ? 'fail' : 'warning'
        message = `转弯半径 ${curve.radius.toFixed(0)}m 小于标准值 ${minRadius}m`
      }

      // 检查超高设置
      const requiredSuperelevation = this.calculateRequiredSuperelevation(
        curve.radius, 
        this.standards.designSpeed
      )
      
      if (requiredSuperelevation > 0.08) { // 超高大于8%时需要警告
        status = status === 'pass' ? 'warning' : status
        message += `，建议设置 ${(requiredSuperelevation * 100).toFixed(1)}% 超高`
      }

      if (status !== 'pass') {
        results.push({
          type: 'radius',
          status,
          value: curve.radius,
          standard: minRadius,
          message,
          location: {
            station: curve.station,
            coordinates: [0, 0, 0] // 需要根据实际情况计算
          }
        })
      }
    }

    return results
  }

  /**
   * 视距检测
   */
  private checkSightDistance(roadData: RoadData): SafetyCheckResult[] {
    const results: SafetyCheckResult[] = []
    const points = roadData.centerline.points

    // 检查停车视距
    for (let i = 0; i < points.length - 1; i += 10) { // 每100米检查一次
      const sightDistance = this.calculateSightDistance(points, i)
      const requiredSightDistance = this.calculateRequiredSightDistance(
        this.standards.designSpeed,
        'stopping'
      )

      let status: 'pass' | 'warning' | 'fail' = 'pass'
      let message = `停车视距 ${sightDistance.toFixed(0)}m 符合要求`

      if (sightDistance < requiredSightDistance) {
        status = sightDistance < requiredSightDistance * 0.8 ? 'fail' : 'warning'
        message = `停车视距 ${sightDistance.toFixed(0)}m 不足，标准值 ${requiredSightDistance.toFixed(0)}m`
      }

      if (status !== 'pass') {
        results.push({
          type: 'sight_distance',
          status,
          value: sightDistance,
          standard: requiredSightDistance,
          message,
          location: {
            station: points[i].station,
            coordinates: [points[i].longitude, points[i].latitude, points[i].elevation]
          }
        })
      }
    }

    return results
  }

  /**
   * 道路宽度检测
   */
  private checkWidth(roadData: RoadData): SafetyCheckResult[] {
    const results: SafetyCheckResult[] = []
    
    // 检查行车道宽度
    const actualWidth = roadData.metadata?.width || this.standards.laneWidth * 2
    const requiredWidth = this.standards.laneWidth * 2 + this.standards.shoulderWidth * 2

    let status: 'pass' | 'warning' | 'fail' = 'pass'
    let message = `道路宽度 ${actualWidth.toFixed(1)}m 符合要求`

    if (actualWidth < requiredWidth) {
      status = actualWidth < requiredWidth * 0.9 ? 'fail' : 'warning'
      message = `道路宽度 ${actualWidth.toFixed(1)}m 不足，标准值 ${requiredWidth.toFixed(1)}m`
    }

    if (status !== 'pass') {
      results.push({
        type: 'width',
        status,
        value: actualWidth,
        standard: requiredWidth,
        message,
        location: {
          station: 0,
          coordinates: [0, 0, 0]
        }
      })
    }

    return results
  }

  /**
   * 净空检测
   */
  private checkClearance(roadData: RoadData): SafetyCheckResult[] {
    const results: SafetyCheckResult[] = []
    
    // 检查垂直净空
    const requiredClearance = 5.0 // 露天矿山道路要求5米净空
    
    // 这里应该检查桥梁、隧道、架空线路等的净空
    // 简化实现，假设有一些净空不足的位置
    
    return results
  }

  /**
   * 排水检测
   */
  private checkDrainage(roadData: RoadData): SafetyCheckResult[] {
    const results: SafetyCheckResult[] = []
    const points = roadData.centerline.points

    // 检查横坡设置
    for (let i = 0; i < points.length; i += 20) { // 每200米检查一次
      const crossSlope = this.calculateCrossSlope(points[i])
      const requiredCrossSlope = 2.0 // 2%横坡

      if (Math.abs(crossSlope) < requiredCrossSlope * 0.5) {
        results.push({
          type: 'drainage',
          status: 'warning',
          value: Math.abs(crossSlope),
          standard: requiredCrossSlope,
          message: `横坡 ${crossSlope.toFixed(1)}% 过小，可能影响排水`,
          location: {
            station: points[i].station,
            coordinates: [points[i].longitude, points[i].latitude, points[i].elevation]
          }
        })
      }
    }

    return results
  }

  /**
   * 边坡稳定性检测
   */
  private checkStability(roadData: RoadData): SafetyCheckResult[] {
    const results: SafetyCheckResult[] = []
    
    // 检查边坡坡率
    const maxCutSlope = 1.0 // 1:1 边坡
    const maxFillSlope = 1.5 // 1:1.5 边坡

    // 这里应该根据横断面数据检查边坡稳定性
    // 简化实现
    
    return results
  }

  /**
   * 交叉口检测
   */
  private checkIntersections(roadData: RoadData): SafetyCheckResult[] {
    const results: SafetyCheckResult[] = []
    
    // 检查交叉口视距、转弯半径等
    // 这里需要与其他道路数据进行交叉分析
    
    return results
  }

  // 辅助方法
  private calculateHorizontalDistance(p1: any, p2: any): number {
    const dx = p2.longitude - p1.longitude
    const dy = p2.latitude - p1.latitude
    return Math.sqrt(dx * dx + dy * dy) * 111320 // 近似转换为米
  }

  private checkLongSlope(points: any[], startIndex: number, gradeThreshold: number): number {
    let length = 0
    for (let i = startIndex; i < points.length - 1; i++) {
      const grade = this.calculateGradeAtPoint(points, i)
      if (Math.abs(grade) < gradeThreshold) break
      
      length += this.calculateHorizontalDistance(points[i], points[i + 1])
    }
    return length
  }

  private calculateGradeAtPoint(points: any[], index: number): number {
    if (index === 0 || index >= points.length - 1) return 0
    
    const prev = points[index - 1]
    const curr = points[index]
    const next = points[index + 1]
    
    const horizontalDist = this.calculateHorizontalDistance(prev, next)
    const verticalDist = next.elevation - prev.elevation
    
    return (verticalDist / horizontalDist) * 100
  }

  private calculateRequiredSuperelevation(radius: number, speed: number): number {
    // 根据设计速度和半径计算所需超高
    const v = speed / 3.6 // 转换为m/s
    const g = 9.81
    const f = 0.15 // 横向摩擦系数
    
    const e = (v * v) / (g * radius) - f
    return Math.max(0, Math.min(0.08, e)) // 限制在0-8%之间
  }

  private calculateSightDistance(points: any[], startIndex: number): number {
    // 简化的视距计算
    // 实际应用中需要考虑地形遮挡、曲线等因素
    const designSpeed = this.standards.designSpeed
    return designSpeed * 2.5 // 简化公式
  }

  private calculateRequiredSightDistance(speed: number, type: 'stopping' | 'passing'): number {
    if (type === 'stopping') {
      // 停车视距计算
      const t = 2.5 // 反应时间(秒)
      const f = 0.35 // 纵向摩擦系数
      const g = 9.81
      const v = speed / 3.6 // 转换为m/s
      
      return v * t + (v * v) / (2 * f * g)
    } else {
      // 超车视距计算
      return speed * 6 // 简化公式
    }
  }

  private calculateCrossSlope(point: any): number {
    // 简化的横坡计算
    // 实际应用中需要根据横断面数据计算
    return 2.0 // 假设2%横坡
  }

  private getAdjustedStandard(key: keyof RoadDesignStandards, baseValue: number): number {
    // 根据严格程度调整标准值
    const adjustmentFactors = {
      strict: 0.9,
      normal: 1.0,
      loose: 1.1
    }
    
    return baseValue * adjustmentFactors[this.config.strictness]
  }

  /**
   * 生成安全检测摘要
   */
  public generateSummary(results: SafetyCheckResult[]): SafetyCheckSummary {
    const passCount = results.filter(r => r.status === 'pass').length
    const warningCount = results.filter(r => r.status === 'warning').length
    const failCount = results.filter(r => r.status === 'fail').length
    const totalChecks = results.length

    // 计算安全评分
    const safetyScore = totalChecks > 0 ? 
      Math.round((passCount * 100 + warningCount * 70) / totalChecks) : 100

    // 找出关键问题
    const criticalIssues = results.filter(r => 
      r.status === 'fail' || (r.status === 'warning' && r.type === 'grade')
    )

    return {
      totalChecks,
      passCount,
      warningCount,
      failCount,
      safetyScore,
      criticalIssues
    }
  }
}

/**
 * 安全检测报告生成器
 */
export class SafetyReportGenerator {
  /**
   * 生成安全检测报告
   */
  public static generateReport(
    roadData: RoadData,
    results: SafetyCheckResult[],
    summary: SafetyCheckSummary
  ): string {
    const report = []
    
    report.push('# 道路安全检测报告')
    report.push('')
    report.push(`## 基本信息`)
    report.push(`- 道路名称: ${roadData.name}`)
    report.push(`- 检测时间: ${new Date().toLocaleString('zh-CN')}`)
    report.push(`- 道路长度: ${roadData.centerline.totalLength.toFixed(0)}m`)
    report.push('')
    
    report.push(`## 检测结果摘要`)
    report.push(`- 安全评分: ${summary.safetyScore}分`)
    report.push(`- 检测项目: ${summary.totalChecks}项`)
    report.push(`- 通过: ${summary.passCount}项`)
    report.push(`- 警告: ${summary.warningCount}项`)
    report.push(`- 不合格: ${summary.failCount}项`)
    report.push('')
    
    if (summary.criticalIssues.length > 0) {
      report.push(`## 关键问题`)
      summary.criticalIssues.forEach((issue, index) => {
        report.push(`${index + 1}. ${issue.message}`)
        report.push(`   - 位置: K${issue.location?.station.toFixed(0)}`)
        report.push(`   - 实测值: ${issue.value.toFixed(2)}`)
        report.push(`   - 标准值: ${issue.standard.toFixed(2)}`)
        report.push('')
      })
    }
    
    report.push(`## 详细检测结果`)
    const groupedResults = this.groupResultsByType(results)
    
    Object.entries(groupedResults).forEach(([type, typeResults]) => {
      report.push(`### ${this.getTypeDisplayName(type)}`)
      typeResults.forEach((result, index) => {
        report.push(`${index + 1}. ${result.message}`)
        if (result.location) {
          report.push(`   - 桩号: K${result.location.station.toFixed(0)}`)
        }
      })
      report.push('')
    })
    
    report.push(`## 改进建议`)
    report.push(this.generateRecommendations(results))
    
    return report.join('\n')
  }

  private static groupResultsByType(results: SafetyCheckResult[]): Record<string, SafetyCheckResult[]> {
    return results.reduce((groups, result) => {
      if (!groups[result.type]) {
        groups[result.type] = []
      }
      groups[result.type].push(result)
      return groups
    }, {} as Record<string, SafetyCheckResult[]>)
  }

  private static getTypeDisplayName(type: string): string {
    const names = {
      grade: '纵坡检测',
      radius: '转弯半径检测',
      sight_distance: '视距检测',
      width: '道路宽度检测',
      clearance: '净空检测',
      drainage: '排水检测',
      stability: '边坡稳定性检测',
      intersection: '交叉口检测'
    }
    return names[type as keyof typeof names] || type
  }

  private static generateRecommendations(results: SafetyCheckResult[]): string {
    const recommendations = []
    
    const failResults = results.filter(r => r.status === 'fail')
    const warningResults = results.filter(r => r.status === 'warning')
    
    if (failResults.length > 0) {
      recommendations.push('### 紧急改进项目')
      failResults.forEach((result, index) => {
        recommendations.push(`${index + 1}. 针对${result.message}，建议立即进行设计调整`)
      })
      recommendations.push('')
    }
    
    if (warningResults.length > 0) {
      recommendations.push('### 建议改进项目')
      warningResults.forEach((result, index) => {
        recommendations.push(`${index + 1}. 针对${result.message}，建议优化设计`)
      })
    }
    
    return recommendations.join('\n')
  }
}
