<template>
  <div class="road-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>道路设计系统</h2>
      <p class="page-description">
        基于露天矿山道路设计标准，提供道路选线、冲突检测、剖切分析、运输路线优化等专业功能
      </p>
    </div>

    <!-- 功能模块选择 -->
    <div class="module-selector">
      <el-card>
        <div class="module-tabs">
          <el-tabs v-model="activeModule" @tab-change="handleModuleChange">
            <el-tab-pane label="道路设计" name="design">
              <template #label>
                <div class="tab-label">
                  <el-icon><Route /></el-icon>
                  <span>道路设计</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="安全检测" name="safety">
              <template #label>
                <div class="tab-label">
                  <el-icon><Shield /></el-icon>
                  <span>安全检测</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="路线优化" name="optimization">
              <template #label>
                <div class="tab-label">
                  <el-icon><MagicStick /></el-icon>
                  <span>路线优化</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="设计标准" name="standards">
              <template #label>
                <div class="tab-label">
                  <el-icon><Document /></el-icon>
                  <span>设计标准</span>
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>

    <!-- 模块内容区 -->
    <div class="module-content">
      <!-- 道路设计模块 -->
      <div v-if="activeModule === 'design'" class="content-panel">
        <RoadDesignPanel />
      </div>

      <!-- 安全检测模块 -->
      <div v-else-if="activeModule === 'safety'" class="content-panel">
        <SafetyCheckPanel />
      </div>

      <!-- 路线优化模块 -->
      <div v-else-if="activeModule === 'optimization'" class="content-panel">
        <RouteOptimizationPanel />
      </div>

      <!-- 设计标准模块 -->
      <div v-else-if="activeModule === 'standards'" class="content-panel">
        <DesignStandardsPanel />
      </div>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-actions" v-if="activeModule === 'design'">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Operation /></el-icon>
            <span>快速操作</span>
          </div>
        </template>

        <div class="action-grid">
          <el-button class="action-button" @click="quickDesign">
            <div class="action-content">
              <el-icon size="24"><MagicStick /></el-icon>
              <span>快速设计</span>
            </div>
          </el-button>

          <el-button class="action-button" @click="importTemplate">
            <div class="action-content">
              <el-icon size="24"><Upload /></el-icon>
              <span>导入模板</span>
            </div>
          </el-button>

          <el-button class="action-button" @click="batchCheck">
            <div class="action-content">
              <el-icon size="24"><Search /></el-icon>
              <span>批量检测</span>
            </div>
          </el-button>

          <el-button class="action-button" @click="generateReport">
            <div class="action-content">
              <el-icon size="24"><Document /></el-icon>
              <span>生成报告</span>
            </div>
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Route,
  Shield,
  MagicStick,
  Document,
  Operation,
  Upload,
  Search
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import RoadDesignPanel from '@/components/road/RoadDesignPanel.vue'
import SafetyCheckPanel from '@/components/safety/SafetyCheckPanel.vue'

// 临时组件占位符
const RouteOptimizationPanel = {
  template: '<div class="placeholder">路线优化模块开发中...</div>'
}
const DesignStandardsPanel = {
  template: '<div class="placeholder">设计标准模块开发中...</div>'
}

// 响应式数据
const activeModule = ref('design')

// 方法
function handleModuleChange(moduleName: string) {
  ElMessage.info(`已切换到${getModuleName(moduleName)}模块`)
}

function getModuleName(module: string): string {
  const names = {
    design: '道路设计',
    safety: '安全检测',
    optimization: '路线优化',
    standards: '设计标准'
  }
  return names[module as keyof typeof names] || module
}

function quickDesign() {
  ElMessage.info('快速设计功能开发中...')
}

function importTemplate() {
  ElMessage.info('导入模板功能开发中...')
}

function batchCheck() {
  ElMessage.info('批量检测功能开发中...')
}

function generateReport() {
  ElMessage.info('生成报告功能开发中...')
}
</script>

<style scoped>
.road-view {
  padding: var(--spacing-lg);
  color: var(--color-text);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.page-header {
  margin-bottom: var(--spacing-md);
}

.page-header h2 {
  color: var(--color-primary);
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-2xl);
}

.page-description {
  color: var(--color-text-soft);
  margin: 0;
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.module-selector {
  flex-shrink: 0;
}

.module-tabs {
  margin: -20px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.module-content {
  flex: 1;
  overflow: hidden;
}

.content-panel {
  height: 100%;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  background-color: var(--color-background-soft);
  border: 2px dashed var(--color-border);
  border-radius: var(--border-radius-base);
  color: var(--color-text-soft);
  font-style: italic;
  font-size: var(--font-size-lg);
}

.quick-actions {
  flex-shrink: 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.action-button {
  height: 80px;
  border: 2px dashed var(--color-border);
  background-color: transparent;
  transition: all 0.3s ease;
}

.action-button:hover {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.action-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-text-soft);
}

.action-button:hover .action-content {
  color: var(--color-primary);
}

/* Element Plus 样式覆盖 */
:deep(.el-tabs__header) {
  margin: 0;
  background-color: var(--color-background-mute);
  border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 var(--spacing-md);
}

:deep(.el-tabs__item) {
  color: var(--color-text-soft);
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: var(--color-primary);
}

:deep(.el-tabs__active-bar) {
  background-color: var(--color-primary);
}

:deep(.el-tabs__content) {
  padding: var(--spacing-lg);
}

:deep(.el-card) {
  background-color: var(--color-background-soft);
  border-color: var(--color-border);
}
</style>
