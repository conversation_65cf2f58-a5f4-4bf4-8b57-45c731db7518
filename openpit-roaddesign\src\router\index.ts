import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      redirect: '/gis'
    },
    {
      path: '/gis',
      name: 'gis',
      component: () => import('../views/GISView.vue'),
      meta: { title: 'GIS数据管理' }
    },
    {
      path: '/data',
      name: 'data',
      component: () => import('../views/DataView.vue'),
      meta: { title: '数据管理' }
    },
    {
      path: '/road',
      name: 'road',
      component: () => import('../views/RoadView.vue'),
      meta: { title: '道路设计' }
    },
    {
      path: '/safety',
      name: 'safety',
      component: () => import('../views/SafetyView.vue'),
      meta: { title: '安全检测' }
    },
    {
      path: '/report',
      name: 'report',
      component: () => import('../views/ReportView.vue'),
      meta: { title: '报告输出' }
    },
    {
      path: '/monitor',
      name: 'monitor',
      component: () => import('../views/MonitorView.vue'),
      meta: { title: '系统监控' }
    }
  ],
})

export default router
