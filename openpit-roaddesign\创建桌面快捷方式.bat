@echo off
chcp 65001 >nul
title 创建桌面快捷方式

echo.
echo ========================================
echo      创建露天矿山道路设计软件快捷方式
echo ========================================
echo.

:: 获取当前脚本目录
set SCRIPT_DIR=%~dp0

:: 获取桌面路径
for /f "tokens=3*" %%i in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop 2^>nul') do set DESKTOP=%%i %%j

if "%DESKTOP%"=="" (
    set DESKTOP=%USERPROFILE%\Desktop
)

echo 📁 项目目录: %SCRIPT_DIR%
echo 🖥️  桌面目录: %DESKTOP%
echo.

:: 创建启动软件的快捷方式
echo 创建"启动露天矿山道路设计软件"快捷方式...

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\启动露天矿山道路设计软件.lnk'); $Shortcut.TargetPath = '%SCRIPT_DIR%启动软件.bat'; $Shortcut.WorkingDirectory = '%SCRIPT_DIR%'; $Shortcut.Description = '露天矿山道路设计软件'; $Shortcut.Save()}"

if exist "%DESKTOP%\启动露天矿山道路设计软件.lnk" (
    echo ✅ 启动快捷方式创建成功
) else (
    echo ❌ 启动快捷方式创建失败
)

:: 创建调试模式的快捷方式
echo 创建"调试模式启动"快捷方式...

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\露天矿山道路设计软件-调试模式.lnk'); $Shortcut.TargetPath = '%SCRIPT_DIR%调试启动.bat'; $Shortcut.WorkingDirectory = '%SCRIPT_DIR%'; $Shortcut.Description = '露天矿山道路设计软件-调试模式'; $Shortcut.Save()}"

if exist "%DESKTOP%\露天矿山道路设计软件-调试模式.lnk" (
    echo ✅ 调试模式快捷方式创建成功
) else (
    echo ❌ 调试模式快捷方式创建失败
)

:: 创建系统诊断的快捷方式
echo 创建"系统诊断"快捷方式...

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\露天矿山道路设计软件-系统诊断.lnk'); $Shortcut.TargetPath = '%SCRIPT_DIR%系统诊断.bat'; $Shortcut.WorkingDirectory = '%SCRIPT_DIR%'; $Shortcut.Description = '露天矿山道路设计软件-系统诊断'; $Shortcut.Save()}"

if exist "%DESKTOP%\露天矿山道路设计软件-系统诊断.lnk" (
    echo ✅ 系统诊断快捷方式创建成功
) else (
    echo ❌ 系统诊断快捷方式创建失败
)

echo.
echo 🎉 快捷方式创建完成！
echo.
echo 📋 已创建的快捷方式:
echo    - 启动露天矿山道路设计软件.lnk
echo    - 露天矿山道路设计软件-调试模式.lnk  
echo    - 露天矿山道路设计软件-系统诊断.lnk
echo.
echo 💡 使用说明:
echo    1. 双击"启动露天矿山道路设计软件"开始使用
echo    2. 如遇问题，先运行"系统诊断"
echo    3. 开发调试时使用"调试模式"
echo.
echo 🌐 启动后访问地址: http://localhost:3000
echo.

pause
