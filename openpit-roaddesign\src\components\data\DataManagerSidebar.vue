<template>
  <div class="data-manager-sidebar">
    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="section-title">
        <el-icon><Operation /></el-icon>
        <span>快速操作</span>
      </div>
      <div class="action-buttons">
        <el-button size="small" type="primary" @click="quickImport">
          <el-icon><Upload /></el-icon>
          快速导入
        </el-button>
        <el-button size="small" @click="createNew">
          <el-icon><Plus /></el-icon>
          新建数据
        </el-button>
      </div>
    </div>

    <!-- 数据类型过滤 -->
    <div class="data-filter">
      <div class="section-title">
        <el-icon><Filter /></el-icon>
        <span>数据类型</span>
      </div>
      <div class="filter-options">
        <el-checkbox-group v-model="selectedTypes" @change="handleTypeFilter">
          <el-checkbox label="terrain">
            <div class="type-option">
              <el-icon><Mountain /></el-icon>
              <span>地形数据</span>
              <el-badge :value="gisStore.getDataCountByType('terrain')" class="type-badge" />
            </div>
          </el-checkbox>
          <el-checkbox label="drilling">
            <div class="type-option">
              <el-icon><Location /></el-icon>
              <span>钻孔数据</span>
              <el-badge :value="gisStore.getDataCountByType('drilling')" class="type-badge" />
            </div>
          </el-checkbox>
          <el-checkbox label="geology">
            <div class="type-option">
              <el-icon><Histogram /></el-icon>
              <span>地质数据</span>
              <el-badge :value="gisStore.getDataCountByType('geology')" class="type-badge" />
            </div>
          </el-checkbox>
          <el-checkbox label="cad">
            <div class="type-option">
              <el-icon><Document /></el-icon>
              <span>CAD数据</span>
              <el-badge :value="gisStore.getDataCountByType('cad')" class="type-badge" />
            </div>
          </el-checkbox>
          <el-checkbox label="road">
            <div class="type-option">
              <el-icon><Route /></el-icon>
              <span>道路数据</span>
              <el-badge :value="gisStore.getDataCountByType('road')" class="type-badge" />
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>

    <!-- 最近使用的数据 -->
    <div class="recent-data">
      <div class="section-title">
        <el-icon><Clock /></el-icon>
        <span>最近使用</span>
      </div>
      <div class="recent-list">
        <div
          v-for="item in recentData"
          :key="item.id"
          class="recent-item"
          @click="selectData(item)"
        >
          <div class="item-icon">
            <el-icon><component :is="getDataIcon(item.type)" /></el-icon>
          </div>
          <div class="item-info">
            <div class="item-name">{{ item.name }}</div>
            <div class="item-time">{{ formatRelativeTime(item.createTime) }}</div>
          </div>
          <div class="item-actions">
            <el-button
              size="small"
              circle
              @click.stop="toggleVisibility(item)"
            >
              <el-icon>
                <View v-if="item.visible" />
                <Hide v-else />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据统计 -->
    <div class="data-stats">
      <div class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        <span>数据统计</span>
      </div>
      <div class="stats-content">
        <div class="stat-item">
          <span class="stat-label">总数据量:</span>
          <span class="stat-value">{{ gisStore.totalDataCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">可见数据:</span>
          <span class="stat-value">{{ gisStore.visibleDataCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">存储空间:</span>
          <span class="stat-value">{{ calculateTotalSize() }}</span>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div class="batch-operations" v-if="selectedData.length > 0">
      <div class="section-title">
        <el-icon><Operation /></el-icon>
        <span>批量操作</span>
      </div>
      <div class="batch-content">
        <el-alert
          :title="`已选择 ${selectedData.length} 项数据`"
          type="info"
          size="small"
          show-icon
          :closable="false"
        />
        <div class="batch-buttons">
          <el-button size="small" @click="batchExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button size="small" @click="batchToggleVisibility">
            <el-icon><View /></el-icon>
            显示/隐藏
          </el-button>
          <el-button size="small" type="danger" @click="batchDelete">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useGISStore } from '@/stores/gis'
import {
  Operation,
  Upload,
  Plus,
  Filter,
  Mountain,
  Location,
  Histogram,
  Document,
  Route,
  Clock,
  View,
  Hide,
  DataAnalysis,
  Download,
  Delete
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { GISData } from '@/types'

const gisStore = useGISStore()

// 响应式数据
const selectedTypes = ref(['terrain', 'drilling', 'geology', 'cad', 'road'])
const selectedData = ref<GISData[]>([])

// 计算属性
const recentData = computed(() => {
  return gisStore.allData
    .slice()
    .sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
    .slice(0, 5)
})

// 方法
function quickImport() {
  ElMessage.info('快速导入功能开发中...')
}

function createNew() {
  ElMessage.info('新建数据功能开发中...')
}

function handleTypeFilter(types: string[]) {
  // 这里可以实现类型过滤逻辑
  ElMessage.info(`已选择类型: ${types.join(', ')}`)
}

function selectData(data: GISData) {
  gisStore.selectData(data)
  ElMessage.success(`已选择数据: ${data.name}`)
}

function toggleVisibility(data: GISData) {
  gisStore.toggleDataVisibility(data.id)
  ElMessage.success(`${data.visible ? '隐藏' : '显示'}数据: ${data.name}`)
}

function getDataIcon(type: string) {
  const icons = {
    terrain: 'Mountain',
    drilling: 'Location',
    geology: 'Histogram',
    cad: 'Document',
    road: 'Route'
  }
  return icons[type as keyof typeof icons] || 'Document'
}

function formatRelativeTime(timeStr: string) {
  const now = new Date()
  const time = new Date(timeStr)
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

function calculateTotalSize() {
  // 模拟计算总存储空间
  const totalMB = gisStore.allData.length * 15.6 // 假设平均每个文件15.6MB
  if (totalMB < 1024) {
    return `${totalMB.toFixed(1)}MB`
  } else {
    return `${(totalMB / 1024).toFixed(1)}GB`
  }
}

function batchExport() {
  ElMessage.success(`导出 ${selectedData.value.length} 个数据文件`)
}

function batchToggleVisibility() {
  ElMessage.success(`批量切换 ${selectedData.value.length} 个数据的可见性`)
}

function batchDelete() {
  ElMessage.success(`批量删除 ${selectedData.value.length} 个数据`)
}
</script>

<style scoped>
.data-manager-sidebar {
  height: 100%;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: bold;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--color-border);
}

.quick-actions,
.data-filter,
.recent-data,
.data-stats,
.batch-operations {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-sm);
  background-color: var(--color-background);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--color-border);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.action-buttons .el-button {
  justify-content: flex-start;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.type-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  width: 100%;
}

.type-badge {
  margin-left: auto;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.recent-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.recent-item:hover {
  background-color: var(--color-background-soft);
}

.item-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

.item-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
}

.stat-label {
  color: var(--color-text-soft);
}

.stat-value {
  color: var(--color-text);
  font-weight: bold;
}

.batch-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.batch-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.batch-buttons .el-button {
  justify-content: flex-start;
}

/* Element Plus 样式覆盖 */
:deep(.el-checkbox) {
  width: 100%;
  margin-right: 0;
}

:deep(.el-checkbox__label) {
  width: 100%;
  padding-left: var(--spacing-xs);
}

:deep(.el-badge__content) {
  background-color: var(--color-primary);
}

:deep(.el-alert) {
  padding: var(--spacing-xs);
}

/* 滚动条样式 */
.data-manager-sidebar::-webkit-scrollbar {
  width: 4px;
}

.data-manager-sidebar::-webkit-scrollbar-track {
  background: var(--color-background);
}

.data-manager-sidebar::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 2px;
}

.data-manager-sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}
</style>
