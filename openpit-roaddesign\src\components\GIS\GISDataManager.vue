<template>
  <div class="gis-data-manager">
    <!-- 数据类型选择 -->
    <div class="data-type-selector">
      <el-radio-group v-model="activeDataType" @change="handleDataTypeChange">
        <el-radio-button label="terrain">地形数据</el-radio-button>
        <el-radio-button label="drilling">钻孔数据</el-radio-button>
        <el-radio-button label="geology">地质数据</el-radio-button>
        <el-radio-button label="cad">CAD数据</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 数据操作工具栏 -->
    <div class="data-toolbar">
      <el-button type="primary" size="small" @click="handleImport">
        <el-icon><Upload /></el-icon>
        导入
      </el-button>
      <el-button type="success" size="small" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出
      </el-button>
      <el-button type="warning" size="small" @click="handleEdit">
        <el-icon><Edit /></el-icon>
        编辑
      </el-button>
      <el-button type="danger" size="small" @click="handleDelete">
        <el-icon><Delete /></el-icon>
        删除
      </el-button>
    </div>

    <!-- 数据列表 -->
    <div class="data-list">
      <el-table
        :data="currentDataList"
        style="width: 100%"
        height="300"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="名称" width="120" />
        <el-table-column prop="type" label="类型" width="80" />
        <el-table-column prop="size" label="大小" width="80" />
        <el-table-column prop="createTime" label="创建时间" width="120" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleView(scope.row)"
            >
              查看
            </el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleToggleVisibility(scope.row)"
            >
              {{ scope.row.visible ? '隐藏' : '显示' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 数据属性面板 -->
    <div class="data-properties" v-if="selectedData">
      <h4>数据属性</h4>
      <el-form :model="selectedData" label-width="80px" size="small">
        <el-form-item label="名称">
          <el-input v-model="selectedData.name" />
        </el-form-item>
        <el-form-item label="类型">
          <el-input v-model="selectedData.type" readonly />
        </el-form-item>
        <el-form-item label="坐标系">
          <el-select v-model="selectedData.coordinateSystem">
            <el-option label="WGS84" value="WGS84" />
            <el-option label="CGCS2000" value="CGCS2000" />
            <el-option label="Beijing54" value="Beijing54" />
            <el-option label="Xian80" value="Xian80" />
          </el-select>
        </el-form-item>
        <el-form-item label="透明度" v-if="selectedData.type !== 'drilling'">
          <el-slider
            v-model="selectedData.opacity"
            :min="0"
            :max="100"
            @change="handleOpacityChange"
          />
        </el-form-item>
        <el-form-item label="颜色" v-if="selectedData.type === 'geology'">
          <el-color-picker v-model="selectedData.color" @change="handleColorChange" />
        </el-form-item>
      </el-form>
    </div>

    <!-- 搜索和过滤 -->
    <div class="data-search">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索数据..."
        @input="handleSearch"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 数据统计 -->
    <div class="data-statistics">
      <el-descriptions title="数据统计" :column="2" size="small">
        <el-descriptions-item label="总数据量">{{ totalDataCount }}</el-descriptions-item>
        <el-descriptions-item label="可见数据">{{ visibleDataCount }}</el-descriptions-item>
        <el-descriptions-item label="地形数据">{{ getDataCountByType('terrain') }}</el-descriptions-item>
        <el-descriptions-item label="钻孔数据">{{ getDataCountByType('drilling') }}</el-descriptions-item>
        <el-descriptions-item label="地质数据">{{ getDataCountByType('geology') }}</el-descriptions-item>
        <el-descriptions-item label="CAD数据">{{ getDataCountByType('cad') }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Upload, Download, Edit, Delete, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 数据接口定义
interface GISData {
  id: string
  name: string
  type: 'terrain' | 'drilling' | 'geology' | 'cad'
  size: string
  createTime: string
  visible: boolean
  opacity: number
  color?: string
  coordinateSystem: string
  filePath?: string
}

// 响应式数据
const activeDataType = ref<string>('terrain')
const searchKeyword = ref<string>('')
const selectedData = ref<GISData | null>(null)
const selectedRows = ref<GISData[]>([])

// 模拟数据
const allData = ref<GISData[]>([
  {
    id: '1',
    name: '主矿区地形',
    type: 'terrain',
    size: '25.6MB',
    createTime: '2024-01-15',
    visible: true,
    opacity: 80,
    coordinateSystem: 'WGS84'
  },
  {
    id: '2',
    name: '钻孔ZK001',
    type: 'drilling',
    size: '1.2MB',
    createTime: '2024-01-16',
    visible: true,
    opacity: 100,
    coordinateSystem: 'CGCS2000'
  },
  {
    id: '3',
    name: '地质构造图',
    type: 'geology',
    size: '8.9MB',
    createTime: '2024-01-17',
    visible: false,
    opacity: 60,
    color: '#ff6b6b',
    coordinateSystem: 'WGS84'
  },
  {
    id: '4',
    name: '道路设计图',
    type: 'cad',
    size: '3.4MB',
    createTime: '2024-01-18',
    visible: true,
    opacity: 90,
    coordinateSystem: 'Beijing54'
  }
])

// 计算属性
const currentDataList = computed(() => {
  let filtered = allData.value.filter(item => item.type === activeDataType.value)
  
  if (searchKeyword.value) {
    filtered = filtered.filter(item => 
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  return filtered
})

const totalDataCount = computed(() => allData.value.length)
const visibleDataCount = computed(() => allData.value.filter(item => item.visible).length)

// 方法
const handleDataTypeChange = (type: string) => {
  selectedData.value = null
  emit('data-type-change', type)
}

const handleImport = () => {
  // 触发文件选择对话框
  const input = document.createElement('input')
  input.type = 'file'
  input.multiple = true
  input.accept = getAcceptTypes()
  input.onchange = (e) => {
    const files = (e.target as HTMLInputElement).files
    if (files) {
      handleFileImport(Array.from(files))
    }
  }
  input.click()
}

const getAcceptTypes = () => {
  switch (activeDataType.value) {
    case 'terrain':
      return '.tif,.dem,.xyz,.las'
    case 'drilling':
      return '.csv,.xlsx,.txt'
    case 'geology':
      return '.shp,.kml,.geojson'
    case 'cad':
      return '.dwg,.dxf,.step'
    default:
      return '*'
  }
}

const handleFileImport = (files: File[]) => {
  files.forEach(file => {
    const newData: GISData = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      name: file.name,
      type: activeDataType.value as any,
      size: formatFileSize(file.size),
      createTime: new Date().toISOString().split('T')[0],
      visible: true,
      opacity: 80,
      coordinateSystem: 'WGS84',
      filePath: URL.createObjectURL(file)
    }
    allData.value.push(newData)
  })
  
  ElMessage.success(`成功导入 ${files.length} 个文件`)
  emit('data-imported', files)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const handleExport = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要导出的数据')
    return
  }
  
  ElMessage.success(`导出 ${selectedRows.value.length} 个数据文件`)
  emit('data-exported', selectedRows.value)
}

const handleEdit = () => {
  if (!selectedData.value) {
    ElMessage.warning('请选择要编辑的数据')
    return
  }
  
  emit('data-edit', selectedData.value)
}

const handleDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }
  
  selectedRows.value.forEach(row => {
    const index = allData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      allData.value.splice(index, 1)
    }
  })
  
  selectedData.value = null
  ElMessage.success(`删除 ${selectedRows.value.length} 个数据文件`)
  emit('data-deleted', selectedRows.value)
}

const handleSelectionChange = (selection: GISData[]) => {
  selectedRows.value = selection
}

const handleRowClick = (row: GISData) => {
  selectedData.value = row
  emit('data-selected', row)
}

const handleView = (row: GISData) => {
  emit('data-view', row)
}

const handleToggleVisibility = (row: GISData) => {
  row.visible = !row.visible
  emit('data-visibility-changed', row)
}

const handleOpacityChange = (value: number) => {
  if (selectedData.value) {
    selectedData.value.opacity = value
    emit('data-opacity-changed', selectedData.value)
  }
}

const handleColorChange = (color: string) => {
  if (selectedData.value) {
    selectedData.value.color = color
    emit('data-color-changed', selectedData.value)
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const getDataCountByType = (type: string): number => {
  return allData.value.filter(item => item.type === type).length
}

// 事件定义
const emit = defineEmits<{
  'data-type-change': [type: string]
  'data-imported': [files: File[]]
  'data-exported': [data: GISData[]]
  'data-edit': [data: GISData]
  'data-deleted': [data: GISData[]]
  'data-selected': [data: GISData]
  'data-view': [data: GISData]
  'data-visibility-changed': [data: GISData]
  'data-opacity-changed': [data: GISData]
  'data-color-changed': [data: GISData]
}>()

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.gis-data-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.data-type-selector {
  padding: 10px 0;
}

.data-toolbar {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.data-list {
  flex: 1;
  min-height: 300px;
}

.data-properties {
  background-color: #404040;
  padding: 15px;
  border-radius: 4px;
}

.data-properties h4 {
  color: #ffd04b;
  margin: 0 0 15px 0;
}

.data-search {
  margin-top: 10px;
}

.data-statistics {
  background-color: #404040;
  padding: 15px;
  border-radius: 4px;
  margin-top: 10px;
}
</style>
