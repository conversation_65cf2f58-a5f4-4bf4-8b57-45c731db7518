<template>
  <div class="data-manager">
    <!-- 数据类型选择 -->
    <div class="data-type-tabs">
      <el-tabs v-model="activeDataType" @tab-change="handleTabChange">
        <el-tab-pane label="地表数据" name="terrain">
          <el-icon><Mountain /></el-icon>
        </el-tab-pane>
        <el-tab-pane label="钻孔数据" name="drilling">
          <el-icon><Location /></el-icon>
        </el-tab-pane>
        <el-tab-pane label="地质数据" name="geology">
          <el-icon><Histogram /></el-icon>
        </el-tab-pane>
        <el-tab-pane label="CAD数据" name="cad">
          <el-icon><Document /></el-icon>
        </el-tab-pane>
        <el-tab-pane label="道路数据" name="road">
          <el-icon><Route /></el-icon>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 工具栏 -->
    <div class="data-toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="showImportDialog">
          <el-icon><Upload /></el-icon>
          导入数据
        </el-button>
        <el-button @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新建数据
        </el-button>
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索数据..."
          style="width: 200px"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 数据列表 -->
    <div class="data-list">
      <el-table
        :data="filteredData"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="名称" min-width="150">
          <template #default="{ row }">
            <div class="data-name">
              <el-icon class="data-icon">
                <component :is="getDataIcon(row.type)" />
              </el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getDataTypeColor(row.type)">
              {{ getDataTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="size" label="大小" width="100" />
        <el-table-column prop="coordinateSystem" label="坐标系" width="120" />
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.visible"
              @change="toggleDataVisibility(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewData(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button size="small" @click="editData(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteData(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 批量操作 -->
    <div class="batch-operations" v-if="selectedData.length > 0">
      <el-alert
        :title="`已选择 ${selectedData.length} 项数据`"
        type="info"
        show-icon
        :closable="false"
      />
      <div class="batch-buttons">
        <el-button @click="batchExport">
          <el-icon><Download /></el-icon>
          批量导出
        </el-button>
        <el-button @click="batchToggleVisibility">
          <el-icon><View /></el-icon>
          批量显示/隐藏
        </el-button>
        <el-button type="danger" @click="batchDelete">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入数据"
      width="600px"
      @close="resetImportDialog"
    >
      <DataImportDialog
        :data-type="activeDataType"
        @import-success="handleImportSuccess"
        @cancel="importDialogVisible = false"
      />
    </el-dialog>

    <!-- 创建数据对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="新建数据"
      width="500px"
      @close="resetCreateDialog"
    >
      <DataCreateDialog
        :data-type="activeDataType"
        @create-success="handleCreateSuccess"
        @cancel="createDialogVisible = false"
      />
    </el-dialog>

    <!-- 数据详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="数据详情"
      width="800px"
    >
      <DataDetailDialog
        :data="currentData"
        @update-success="handleUpdateSuccess"
        @close="detailDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useGISStore } from '@/stores/gis'
import {
  Mountain,
  Location,
  Histogram,
  Document,
  Route,
  Upload,
  Plus,
  Refresh,
  Search,
  View,
  Edit,
  Delete,
  Download
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { GISData } from '@/types'
import DataImportDialog from './DataImportDialog.vue'
import DataCreateDialog from './DataCreateDialog.vue'
import DataDetailDialog from './DataDetailDialog.vue'

const gisStore = useGISStore()

// 响应式数据
const activeDataType = ref('terrain')
const searchKeyword = ref('')
const selectedData = ref<GISData[]>([])
const currentData = ref<GISData | null>(null)

// 对话框状态
const importDialogVisible = ref(false)
const createDialogVisible = ref(false)
const detailDialogVisible = ref(false)

// 计算属性
const filteredData = computed(() => {
  let data = gisStore.currentDataList
  
  if (searchKeyword.value) {
    data = data.filter(item => 
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.coordinateSystem.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }
  
  return data
})

onMounted(() => {
  gisStore.setActiveDataType(activeDataType.value as any)
})

// 方法
function handleTabChange(tabName: string) {
  gisStore.setActiveDataType(tabName as any)
  selectedData.value = []
}

function handleSelectionChange(selection: GISData[]) {
  selectedData.value = selection
}

function handleRowClick(row: GISData) {
  currentData.value = row
  gisStore.selectData(row)
}

function getDataIcon(type: string) {
  const icons = {
    terrain: 'Mountain',
    drilling: 'Location',
    geology: 'Histogram',
    cad: 'Document',
    road: 'Route'
  }
  return icons[type as keyof typeof icons] || 'Document'
}

function getDataTypeColor(type: string) {
  const colors = {
    terrain: 'success',
    drilling: 'warning',
    geology: 'danger',
    cad: 'info',
    road: 'primary'
  }
  return colors[type as keyof typeof colors] || 'info'
}

function getDataTypeName(type: string) {
  const names = {
    terrain: '地形',
    drilling: '钻孔',
    geology: '地质',
    cad: 'CAD',
    road: '道路'
  }
  return names[type as keyof typeof names] || type
}

function formatTime(timeStr: string) {
  return new Date(timeStr).toLocaleString('zh-CN')
}

function toggleDataVisibility(data: GISData) {
  gisStore.toggleDataVisibility(data.id)
  ElMessage.success(`${data.visible ? '显示' : '隐藏'}数据: ${data.name}`)
}

// 对话框操作
function showImportDialog() {
  importDialogVisible.value = true
}

function showCreateDialog() {
  createDialogVisible.value = true
}

function resetImportDialog() {
  // 重置导入对话框状态
}

function resetCreateDialog() {
  // 重置创建对话框状态
}

function handleImportSuccess(data: GISData[]) {
  data.forEach(item => gisStore.addData(item))
  importDialogVisible.value = false
  ElMessage.success(`成功导入 ${data.length} 个数据文件`)
}

function handleCreateSuccess(data: GISData) {
  gisStore.addData(data)
  createDialogVisible.value = false
  ElMessage.success(`成功创建数据: ${data.name}`)
}

function handleUpdateSuccess(data: GISData) {
  gisStore.updateData(data.id, data)
  detailDialogVisible.value = false
  ElMessage.success(`成功更新数据: ${data.name}`)
}

// 数据操作
function refreshData() {
  ElMessage.success('数据已刷新')
  // 这里可以实现实际的数据刷新逻辑
}

function viewData(data: GISData) {
  currentData.value = data
  detailDialogVisible.value = true
}

function editData(data: GISData) {
  currentData.value = data
  detailDialogVisible.value = true
}

function deleteData(data: GISData) {
  ElMessageBox.confirm(
    `确定要删除数据 "${data.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    gisStore.removeData(data.id)
    ElMessage.success('数据已删除')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 批量操作
function batchExport() {
  const ids = selectedData.value.map(item => item.id)
  gisStore.exportData(ids, 'geojson')
  ElMessage.success(`导出 ${selectedData.value.length} 个数据文件`)
}

function batchToggleVisibility() {
  const allVisible = selectedData.value.every(item => item.visible)
  selectedData.value.forEach(item => {
    gisStore.setDataVisibility(item.id, !allVisible)
  })
  ElMessage.success(`批量${allVisible ? '隐藏' : '显示'}数据`)
}

function batchDelete() {
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedData.value.length} 个数据吗？`,
    '确认批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    selectedData.value.forEach(item => {
      gisStore.removeData(item.id)
    })
    selectedData.value = []
    ElMessage.success('批量删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}
</script>

<style scoped>
.data-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
}

.data-type-tabs {
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-background-soft);
  padding: 0 var(--spacing-md);
}

.data-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-background-mute);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.data-list {
  flex: 1;
  overflow: auto;
  padding: var(--spacing-md);
}

.data-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.data-icon {
  color: var(--color-primary);
}

.batch-operations {
  padding: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  background-color: var(--color-background-soft);
}

.batch-buttons {
  margin-top: var(--spacing-sm);
  display: flex;
  gap: var(--spacing-sm);
}

/* Element Plus 样式覆盖 */
:deep(.el-table) {
  background-color: var(--color-background);
}

:deep(.el-table th) {
  background-color: var(--color-background-mute);
  color: var(--color-text);
}

:deep(.el-table td) {
  border-bottom: 1px solid var(--color-border);
}

:deep(.el-table__row:hover) {
  background-color: var(--color-background-soft);
}

:deep(.el-tabs__item) {
  color: var(--color-text-soft);
}

:deep(.el-tabs__item.is-active) {
  color: var(--color-primary);
}

:deep(.el-tabs__active-bar) {
  background-color: var(--color-primary);
}
</style>
