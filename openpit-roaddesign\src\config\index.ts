import type { ProjectConfig, CoordinateSystem, RoadDesignStandards } from '@/types'

// 默认坐标系统
export const DEFAULT_COORDINATE_SYSTEMS: CoordinateSystem[] = [
  {
    name: 'WGS84',
    code: 'EPSG:4326',
    proj4: '+proj=longlat +datum=WGS84 +no_defs',
    wkt: 'GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563,AUTHORITY["EPSG","7030"]],AUTHORITY["EPSG","6326"]],<PERSON>IM<PERSON>["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4326"]]'
  },
  {
    name: 'CGCS2000',
    code: 'EPSG:4490',
    proj4: '+proj=longlat +datum=CGCS2000 +no_defs',
    wkt: 'GEOGCS["China Geodetic Coordinate System 2000",DATUM["China_2000",SPHEROID["CGCS2000",6378137,298.257222101,AUTHORITY["EPSG","1024"]],AUTHORITY["EPSG","1043"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4490"]]'
  },
  {
    name: 'Beijing54',
    code: 'EPSG:4214',
    proj4: '+proj=longlat +datum=beijing +no_defs',
    wkt: 'GEOGCS["Beijing 1954",DATUM["Beijing_1954",SPHEROID["Krassowsky 1940",6378245,298.3,AUTHORITY["EPSG","7024"]],AUTHORITY["EPSG","6214"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4214"]]'
  },
  {
    name: 'Xian80',
    code: 'EPSG:4610',
    proj4: '+proj=longlat +datum=xian80 +no_defs',
    wkt: 'GEOGCS["Xian 1980",DATUM["Xian_1980",SPHEROID["IAG 1975",6378140,298.257,AUTHORITY["EPSG","7049"]],AUTHORITY["EPSG","6610"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4610"]]'
  }
]

// 露天矿山道路设计标准
export const OPENPIT_ROAD_STANDARDS: Record<string, RoadDesignStandards> = {
  main: {
    minRadius: 50, // 主要道路最小转弯半径(m)
    maxGrade: 8, // 最大纵坡(%)
    minSightDistance: 100, // 最小视距(m)
    designSpeed: 40, // 设计速度(km/h)
    laneWidth: 3.5, // 车道宽度(m)
    shoulderWidth: 1.0 // 路肩宽度(m)
  },
  branch: {
    minRadius: 30,
    maxGrade: 10,
    minSightDistance: 75,
    designSpeed: 30,
    laneWidth: 3.0,
    shoulderWidth: 0.75
  },
  access: {
    minRadius: 20,
    maxGrade: 12,
    minSightDistance: 50,
    designSpeed: 20,
    laneWidth: 2.5,
    shoulderWidth: 0.5
  },
  haul: {
    minRadius: 80, // 运输道路要求更大转弯半径
    maxGrade: 6, // 更小纵坡以适应重载车辆
    minSightDistance: 150,
    designSpeed: 50,
    laneWidth: 4.0, // 更宽车道
    shoulderWidth: 1.5
  }
}

// 文件类型配置
export const FILE_TYPES = {
  terrain: {
    extensions: ['.tif', '.tiff', '.dem', '.xyz', '.las', '.laz'],
    mimeTypes: ['image/tiff', 'application/octet-stream', 'text/plain'],
    maxSize: 100 * 1024 * 1024 // 100MB
  },
  drilling: {
    extensions: ['.csv', '.xlsx', '.xls', '.txt'],
    mimeTypes: ['text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/plain'],
    maxSize: 10 * 1024 * 1024 // 10MB
  },
  geology: {
    extensions: ['.shp', '.kml', '.kmz', '.geojson', '.gml'],
    mimeTypes: ['application/octet-stream', 'application/vnd.google-earth.kml+xml', 'application/json'],
    maxSize: 50 * 1024 * 1024 // 50MB
  },
  cad: {
    extensions: ['.dwg', '.dxf', '.step', '.stp', '.iges', '.igs'],
    mimeTypes: ['application/octet-stream'],
    maxSize: 200 * 1024 * 1024 // 200MB
  }
}

// 默认项目配置
export const DEFAULT_PROJECT_CONFIG: ProjectConfig = {
  name: '露天矿山道路设计项目',
  description: '基于Cesium的露天矿山道路设计系统',
  coordinateSystem: DEFAULT_COORDINATE_SYSTEMS[0],
  workArea: {
    center: [116.3974, 39.9093], // 北京坐标
    bounds: [116.0, 39.5, 117.0, 40.5]
  },
  units: {
    length: 'meter',
    area: 'square_meter',
    volume: 'cubic_meter'
  }
}

// Cesium配置
export const CESIUM_CONFIG = {
  ion: {
    defaultAccessToken: import.meta.env.VITE_CESIUM_TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.rw6p6RtWKaZhKdIh4tLVb6YQJ9RbD8VJ5n4n4n4n4n4' // 开发用token
  },
  viewer: {
    animation: false,
    baseLayerPicker: true,
    fullscreenButton: false,
    geocoder: false,
    homeButton: false,
    infoBox: true,
    sceneModePicker: true,
    selectionIndicator: true,
    timeline: false,
    navigationHelpButton: false,
    scene3DOnly: false,
    clockViewModel: undefined,
    selectedImageryProviderViewModel: undefined,
    imageryProviderViewModels: undefined,
    selectedTerrainProviderViewModel: undefined,
    terrainProviderViewModels: undefined,
    skyBox: undefined,
    skyAtmosphere: undefined,
    fullscreenElement: undefined,
    useDefaultRenderLoop: true,
    targetFrameRate: undefined,
    showRenderLoopErrors: true,
    automaticallyTrackDataSourceClocks: true,
    contextOptions: undefined,
    sceneMode: undefined,
    mapProjection: undefined,
    globe: undefined,
    orderIndependentTranslucency: true,
    creditContainer: undefined,
    creditViewport: undefined,
    dataSources: undefined,
    terrainExaggeration: 1.0,
    shadows: false,
    terrainShadows: undefined,
    mapMode2D: undefined,
    projectionPicker: false,
    requestRenderMode: false,
    maximumRenderTimeChange: 0.0
  },
  terrain: {
    url: 'https://assets.agi.com/stk-terrain/world',
    requestVertexNormals: true,
    requestWaterMask: true
  },
  imagery: {
    url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer',
    layers: '',
    enablePickFeatures: false
  }
}

// 主题配置
export const THEME_CONFIG = {
  dark: {
    primary: '#ffd04b', // 黄色主色调
    secondary: '#404040', // 深灰色
    background: '#1a1a1a', // 黑色背景
    surface: '#2d2d2d', // 表面颜色
    text: '#ffffff', // 白色文字
    textSecondary: '#cccccc', // 次要文字颜色
    border: '#555555', // 边框颜色
    success: '#67c23a',
    warning: '#e6a23c',
    danger: '#f56c6c',
    info: '#909399'
  },
  light: {
    primary: '#409eff',
    secondary: '#f5f5f5',
    background: '#ffffff',
    surface: '#f8f9fa',
    text: '#303133',
    textSecondary: '#606266',
    border: '#dcdfe6',
    success: '#67c23a',
    warning: '#e6a23c',
    danger: '#f56c6c',
    info: '#909399'
  }
}

// API配置
export const API_CONFIG = {
  baseURL: process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:8080/api',
  timeout: 30000,
  retryTimes: 3,
  retryDelay: 1000
}

// 地图图层配置
export const MAP_LAYERS = {
  base: [
    {
      name: '卫星影像',
      type: 'imagery',
      url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer',
      visible: true
    },
    {
      name: '地形图',
      type: 'imagery',
      url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Topo_Map/MapServer',
      visible: false
    }
  ],
  terrain: [
    {
      name: 'Cesium World Terrain',
      type: 'terrain',
      url: 'https://assets.agi.com/stk-terrain/world',
      visible: true
    }
  ]
}

// 性能配置
export const PERFORMANCE_CONFIG = {
  maxEntities: 10000, // 最大实体数量
  maxPrimitives: 5000, // 最大图元数量
  lodThreshold: 1000, // LOD阈值
  cullingEnabled: true, // 启用视锥体剔除
  frustumCulling: true, // 启用视锥体裁剪
  occlusionCulling: true, // 启用遮挡剔除
  dynamicScreenSpaceError: true, // 动态屏幕空间误差
  preloadSiblings: true, // 预加载兄弟节点
  preferLeaves: true // 优先加载叶子节点
}

// 导出配置
export const EXPORT_CONFIG = {
  formats: {
    image: ['png', 'jpg', 'jpeg'],
    vector: ['geojson', 'kml', 'shp'],
    cad: ['dxf', 'dwg'],
    report: ['pdf', 'docx', 'html']
  },
  quality: {
    low: 0.5,
    medium: 0.8,
    high: 1.0
  }
}
