@echo off
chcp 65001 >nul
title 露天矿山道路设计软件调试启动器

echo.
echo ========================================
echo   露天矿山道路设计软件调试启动器 v1.0
echo ========================================
echo.

:: 设置调试环境变量
set NODE_ENV=development
set VITE_DEBUG_MODE=true
set VITE_LOG_LEVEL=debug
set VITE_SHOW_PERFORMANCE_MONITOR=true
set VITE_ENABLE_DEVTOOLS=true

echo 🔧 调试模式配置:
echo    - 调试模式: 启用
echo    - 日志级别: DEBUG
echo    - 性能监控: 启用
echo    - 开发工具: 启用
echo    - 热重载: 启用
echo.

:: 检查是否需要安装依赖
if not exist "node_modules" (
    echo 📦 正在安装项目依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

:: 清理缓存
echo 🧹 清理开发缓存...
if exist ".vite" rmdir /s /q ".vite"
if exist "dist" rmdir /s /q "dist"
if exist "node_modules/.vite" rmdir /s /q "node_modules/.vite"

:: 启动类型检查
echo 🔍 启动TypeScript类型检查...
start "TypeScript检查" cmd /c "npm run type-check -- --watch"

:: 等待一秒让类型检查启动
timeout /t 1 /nobreak >nul

:: 启动测试监控
echo 🧪 启动测试监控...
start "测试监控" cmd /c "npm run test:watch"

:: 等待一秒让测试启动
timeout /t 1 /nobreak >nul

:: 启动开发服务器
echo 🚀 启动调试开发服务器...
echo.
echo 📊 调试信息:
echo    - 主服务器: http://localhost:3000
echo    - 测试界面: http://localhost:51204/__vitest__/
echo    - 性能监控: 浏览器开发者工具
echo.
echo 💡 调试提示:
echo    - 打开浏览器开发者工具查看详细日志
echo    - 使用 Vue DevTools 扩展调试组件
echo    - 查看网络面板监控API请求
echo    - 使用性能面板分析性能问题
echo.
echo 按 Ctrl+C 停止所有服务
echo.

:: 启动主服务器
npm run dev

echo.
echo ⚠️  调试服务器已停止
echo 正在关闭相关进程...

:: 尝试关闭相关进程
taskkill /f /im node.exe 2>nul
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq TypeScript检查*" 2>nul
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq 测试监控*" 2>nul

pause
