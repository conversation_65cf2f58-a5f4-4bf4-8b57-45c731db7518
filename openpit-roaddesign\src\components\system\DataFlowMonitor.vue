<template>
  <div class="data-flow-monitor">
    <!-- 数据流状态概览 -->
    <div class="flow-overview">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>数据流状态监控</span>
            <div class="header-actions">
              <el-switch
                v-model="autoRefresh"
                active-text="自动刷新"
                @change="toggleAutoRefresh"
              />
              <el-button size="small" @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <div class="flow-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-icon active">
                  <el-icon><Connection /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ flowStats.activeConnections }}</div>
                  <div class="stat-label">活跃连接</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-icon events">
                  <el-icon><Bell /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ flowStats.totalEvents }}</div>
                  <div class="stat-label">总事件数</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-icon sync">
                  <el-icon><Refresh /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ flowStats.syncOperations }}</div>
                  <div class="stat-label">同步操作</div>
                </div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card">
                <div class="stat-icon errors">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ flowStats.errors }}</div>
                  <div class="stat-label">错误数量</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 模块连接图 -->
    <div class="module-connections">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Share /></el-icon>
            <span>模块连接图</span>
          </div>
        </template>

        <div class="connection-diagram">
          <div class="modules-grid">
            <div
              v-for="module in modules"
              :key="module.id"
              class="module-node"
              :class="{ 
                active: module.active, 
                selected: selectedModule === module.id 
              }"
              @click="selectModule(module.id)"
            >
              <div class="module-icon">
                <el-icon>
                  <component :is="module.icon" />
                </el-icon>
              </div>
              <div class="module-name">{{ module.name }}</div>
              <div class="module-status">
                <el-tag :type="getModuleStatusType(module.status)" size="small">
                  {{ module.status }}
                </el-tag>
              </div>
              <div class="module-data-count">{{ module.dataCount }} 项数据</div>
            </div>
          </div>

          <!-- 连接线 -->
          <svg class="connection-lines" viewBox="0 0 800 600">
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                      refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#409eff" />
              </marker>
            </defs>
            <path
              v-for="connection in connections"
              :key="`${connection.from}-${connection.to}`"
              :d="connection.path"
              :class="['connection-line', { active: connection.active }]"
              marker-end="url(#arrowhead)"
            />
          </svg>
        </div>
      </el-card>
    </div>

    <!-- 事件历史 -->
    <div class="event-history">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>事件历史</span>
            <div class="header-actions">
              <el-select v-model="eventFilter" size="small" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="数据更新" value="data_updated" />
                <el-option label="模块切换" value="module_switched" />
                <el-option label="安全检测" value="safety_checked" />
                <el-option label="道路设计" value="road_designed" />
              </el-select>
              <el-button size="small" @click="clearHistory">
                <el-icon><Delete /></el-icon>
                清空
              </el-button>
            </div>
          </div>
        </template>

        <div class="event-list">
          <div
            v-for="(event, index) in filteredEvents"
            :key="index"
            class="event-item"
            :class="getEventTypeClass(event.type)"
          >
            <div class="event-header">
              <div class="event-type">
                <el-icon>
                  <component :is="getEventIcon(event.type)" />
                </el-icon>
                <span>{{ getEventTypeName(event.type) }}</span>
              </div>
              <div class="event-time">{{ formatTime(event.timestamp) }}</div>
            </div>
            <div class="event-details">
              <span class="event-source">来源: {{ event.source }}</span>
              <span v-if="event.target" class="event-target">目标: {{ event.target }}</span>
            </div>
            <div class="event-data" v-if="event.metadata">
              <el-tag
                v-for="(value, key) in event.metadata"
                :key="key"
                size="small"
                type="info"
              >
                {{ key }}: {{ value }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据同步设置 -->
    <div class="sync-settings">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>数据同步设置</span>
          </div>
        </template>

        <div class="sync-options">
          <div class="sync-option">
            <el-switch
              v-model="syncSettings.gisToRoad"
              @change="updateSyncSetting('gisToRoad', $event)"
            />
            <span class="sync-label">GIS → 道路设计</span>
            <span class="sync-description">GIS数据变化时自动同步到道路设计模块</span>
          </div>
          <div class="sync-option">
            <el-switch
              v-model="syncSettings.roadToSafety"
              @change="updateSyncSetting('roadToSafety', $event)"
            />
            <span class="sync-label">道路设计 → 安全检测</span>
            <span class="sync-description">道路设计完成时自动触发安全检测</span>
          </div>
          <div class="sync-option">
            <el-switch
              v-model="syncSettings.safetyToReport"
              @change="updateSyncSetting('safetyToReport', $event)"
            />
            <span class="sync-label">安全检测 → 报告生成</span>
            <span class="sync-description">安全检测完成时自动生成报告</span>
          </div>
          <div class="sync-option">
            <el-switch
              v-model="syncSettings.dataToGis"
              @change="updateSyncSetting('dataToGis', $event)"
            />
            <span class="sync-label">数据管理 → GIS</span>
            <span class="sync-description">数据管理操作时自动更新GIS显示</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import {
  DataAnalysis,
  Refresh,
  Connection,
  Bell,
  Warning,
  Share,
  Clock,
  Delete,
  Setting,
  Database,
  Route,
  Shield,
  Document,
  Monitor,
  Location
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { dataFlowManager, onDataFlowEvent, offDataFlowEvent } from '@/utils/dataFlowManager'
import { moduleIntegrationService } from '@/services/moduleIntegrationService'
import type { DataFlowEvent } from '@/types'

// 响应式数据
const autoRefresh = ref(true)
const selectedModule = ref('')
const eventFilter = ref('all')
let refreshTimer: number | null = null

// 数据流统计
const flowStats = reactive({
  activeConnections: 5,
  totalEvents: 0,
  syncOperations: 0,
  errors: 0
})

// 模块定义
const modules = ref([
  { id: 'gis', name: 'GIS系统', icon: 'Location', active: true, status: 'running', dataCount: 12 },
  { id: 'data', name: '数据管理', icon: 'Database', active: true, status: 'running', dataCount: 8 },
  { id: 'road', name: '道路设计', icon: 'Route', active: false, status: 'idle', dataCount: 3 },
  { id: 'safety', name: '安全检测', icon: 'Shield', active: false, status: 'idle', dataCount: 5 },
  { id: 'report', name: '报告输出', icon: 'Document', active: false, status: 'idle', dataCount: 2 },
  { id: 'monitor', name: '系统监控', icon: 'Monitor', active: true, status: 'running', dataCount: 1 }
])

// 连接定义
const connections = ref([
  { from: 'gis', to: 'road', active: true, path: 'M 150 100 Q 300 50 450 100' },
  { from: 'road', to: 'safety', active: false, path: 'M 450 100 Q 600 50 750 100' },
  { from: 'safety', to: 'report', active: false, path: 'M 750 100 Q 600 150 450 200' },
  { from: 'data', to: 'gis', active: true, path: 'M 150 200 Q 100 150 150 100' }
])

// 事件历史
const eventHistory = ref<DataFlowEvent[]>([])

// 同步设置
const syncSettings = reactive({
  gisToRoad: true,
  roadToSafety: true,
  safetyToReport: true,
  dataToGis: true
})

// 计算属性
const filteredEvents = computed(() => {
  if (eventFilter.value === 'all') {
    return eventHistory.value
  }
  return eventHistory.value.filter(event => event.type === eventFilter.value)
})

// 方法
function toggleAutoRefresh(enabled: boolean) {
  if (enabled) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

function startAutoRefresh() {
  refreshTimer = window.setInterval(() => {
    refreshData()
  }, 5000)
}

function stopAutoRefresh() {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

function refreshData() {
  // 更新统计数据
  const history = dataFlowManager.getEventHistory()
  flowStats.totalEvents = history.length
  flowStats.syncOperations = history.filter(e => e.type === 'data_updated').length
  flowStats.errors = history.filter(e => e.metadata?.error).length
  
  // 更新事件历史
  eventHistory.value = history.slice(-50) // 只保留最近50个事件
  
  // 更新模块状态
  updateModuleStates()
}

function updateModuleStates() {
  modules.value.forEach(module => {
    const state = dataFlowManager.getModuleState(module.id)
    if (state) {
      module.active = state.active
      module.status = state.active ? 'running' : 'idle'
      module.dataCount = state.selectedData.length
    }
  })
}

function selectModule(moduleId: string) {
  selectedModule.value = moduleId
  ElMessage.info(`已选择模块: ${modules.value.find(m => m.id === moduleId)?.name}`)
}

function clearHistory() {
  dataFlowManager.clearCache()
  eventHistory.value = []
  flowStats.totalEvents = 0
  flowStats.syncOperations = 0
  flowStats.errors = 0
  ElMessage.success('事件历史已清空')
}

function updateSyncSetting(setting: string, enabled: boolean) {
  moduleIntegrationService.setDataSync(setting as any, enabled)
  ElMessage.success(`${enabled ? '启用' : '禁用'}了${getSyncSettingName(setting)}`)
}

function getSyncSettingName(setting: string): string {
  const names = {
    gisToRoad: 'GIS到道路设计的数据同步',
    roadToSafety: '道路设计到安全检测的数据同步',
    safetyToReport: '安全检测到报告生成的数据同步',
    dataToGis: '数据管理到GIS的数据同步'
  }
  return names[setting as keyof typeof names] || setting
}

function getModuleStatusType(status: string): string {
  const types = {
    running: 'success',
    idle: 'info',
    error: 'danger',
    warning: 'warning'
  }
  return types[status as keyof typeof types] || 'info'
}

function getEventTypeClass(type: string): string {
  return `event-${type.replace('_', '-')}`
}

function getEventIcon(type: string): string {
  const icons = {
    data_updated: 'Refresh',
    module_switched: 'Share',
    safety_checked: 'Shield',
    road_designed: 'Route',
    data_imported: 'Database',
    selection_changed: 'Location'
  }
  return icons[type as keyof typeof icons] || 'Bell'
}

function getEventTypeName(type: string): string {
  const names = {
    data_updated: '数据更新',
    module_switched: '模块切换',
    safety_checked: '安全检测',
    road_designed: '道路设计',
    data_imported: '数据导入',
    selection_changed: '选择变化'
  }
  return names[type as keyof typeof names] || type
}

function formatTime(timestamp: number): string {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN')
}

// 事件处理
function handleDataFlowEvent(event: DataFlowEvent) {
  eventHistory.value.unshift(event)
  if (eventHistory.value.length > 100) {
    eventHistory.value.pop()
  }
  
  // 更新统计
  flowStats.totalEvents++
  if (event.type === 'data_updated') {
    flowStats.syncOperations++
  }
  if (event.metadata?.error) {
    flowStats.errors++
  }
}

// 生命周期
onMounted(() => {
  // 注册事件监听
  const eventTypes = ['data_updated', 'module_switched', 'safety_checked', 'road_designed', 'data_imported', 'selection_changed']
  eventTypes.forEach(type => {
    onDataFlowEvent(type as any, handleDataFlowEvent)
  })
  
  // 初始化数据
  refreshData()
  
  // 启动自动刷新
  if (autoRefresh.value) {
    startAutoRefresh()
  }
})

onUnmounted(() => {
  // 清理定时器
  stopAutoRefresh()
  
  // 移除事件监听
  const eventTypes = ['data_updated', 'module_switched', 'safety_checked', 'road_designed', 'data_imported', 'selection_changed']
  eventTypes.forEach(type => {
    offDataFlowEvent(type as any, handleDataFlowEvent)
  })
})
</script>

<style scoped>
.data-flow-monitor {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  height: 100%;
  overflow: auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.header-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.flow-stats {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--color-background);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--color-border);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-icon.active {
  background-color: rgba(103, 194, 58, 0.1);
  color: var(--color-success);
}

.stat-icon.events {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
}

.stat-icon.sync {
  background-color: rgba(230, 162, 60, 0.1);
  color: var(--color-warning);
}

.stat-icon.errors {
  background-color: rgba(245, 108, 108, 0.1);
  color: var(--color-danger);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: bold;
  color: var(--color-text);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
  margin-top: var(--spacing-xs);
}

.connection-diagram {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: var(--spacing-lg);
  height: 100%;
  padding: var(--spacing-lg);
}

.module-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
  background-color: var(--color-background);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  transition: all 0.3s ease;
}

.module-node:hover {
  border-color: var(--color-primary);
  background-color: var(--color-background-soft);
}

.module-node.active {
  border-color: var(--color-success);
  background-color: rgba(103, 194, 58, 0.05);
}

.module-node.selected {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.module-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: var(--spacing-sm);
}

.module-name {
  font-weight: bold;
  color: var(--color-text);
  margin-bottom: var(--spacing-xs);
}

.module-status {
  margin-bottom: var(--spacing-xs);
}

.module-data-count {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-line {
  fill: none;
  stroke: var(--color-border);
  stroke-width: 2;
  stroke-dasharray: 5,5;
  opacity: 0.5;
}

.connection-line.active {
  stroke: var(--color-primary);
  stroke-dasharray: none;
  opacity: 1;
}

.event-list {
  max-height: 400px;
  overflow-y: auto;
}

.event-item {
  padding: var(--spacing-sm);
  border-left: 3px solid var(--color-border);
  margin-bottom: var(--spacing-sm);
  background-color: var(--color-background);
  border-radius: var(--border-radius-small);
}

.event-item.event-data-updated {
  border-left-color: var(--color-primary);
}

.event-item.event-safety-checked {
  border-left-color: var(--color-warning);
}

.event-item.event-road-designed {
  border-left-color: var(--color-success);
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.event-type {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 500;
  color: var(--color-text);
}

.event-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

.event-details {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
  margin-bottom: var(--spacing-xs);
}

.event-data {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.sync-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.sync-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--color-background);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--color-border);
}

.sync-label {
  font-weight: 500;
  color: var(--color-text);
  min-width: 150px;
}

.sync-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
  flex: 1;
}

/* 滚动条样式 */
.event-list::-webkit-scrollbar {
  width: 4px;
}

.event-list::-webkit-scrollbar-track {
  background: var(--color-background);
}

.event-list::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 2px;
}

.event-list::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}
</style>
