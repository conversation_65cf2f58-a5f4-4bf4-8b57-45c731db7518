<template>
  <div class="road-design-panel">
    <!-- 设计工具栏 -->
    <div class="design-toolbar">
      <div class="toolbar-section">
        <div class="section-title">道路类型</div>
        <el-select v-model="designParams.roadType" @change="updateDesignStandards">
          <el-option label="主要道路" value="main" />
          <el-option label="分支道路" value="branch" />
          <el-option label="通道道路" value="access" />
          <el-option label="运输道路" value="haul" />
        </el-select>
      </div>

      <div class="toolbar-section">
        <div class="section-title">设计参数</div>
        <div class="param-group">
          <el-input-number
            v-model="designParams.width"
            :min="3"
            :max="20"
            :step="0.5"
            size="small"
            controls-position="right"
          />
          <span class="param-label">宽度(m)</span>
        </div>
        <div class="param-group">
          <el-input-number
            v-model="designParams.designSpeed"
            :min="10"
            :max="80"
            :step="5"
            size="small"
            controls-position="right"
          />
          <span class="param-label">设计速度(km/h)</span>
        </div>
      </div>

      <div class="toolbar-section">
        <div class="section-title">设计工具</div>
        <el-button-group>
          <el-button 
            size="small" 
            :type="activeDesignTool === 'alignment' ? 'primary' : 'default'"
            @click="setDesignTool('alignment')"
          >
            <el-icon><Route /></el-icon>
            选线
          </el-button>
          <el-button 
            size="small"
            :type="activeDesignTool === 'profile' ? 'primary' : 'default'"
            @click="setDesignTool('profile')"
          >
            <el-icon><TrendCharts /></el-icon>
            剖面
          </el-button>
          <el-button 
            size="small"
            :type="activeDesignTool === 'optimize' ? 'primary' : 'default'"
            @click="setDesignTool('optimize')"
          >
            <el-icon><MagicStick /></el-icon>
            优化
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 设计内容区 -->
    <div class="design-content">
      <!-- 道路选线 -->
      <div v-if="activeDesignTool === 'alignment'" class="design-section">
        <RoadAlignmentDesign
          :design-params="designParams"
          :terrain-data="terrainData"
          @alignment-created="handleAlignmentCreated"
        />
      </div>

      <!-- 道路剖面 -->
      <div v-else-if="activeDesignTool === 'profile'" class="design-section">
        <RoadProfileDesign
          :road-data="currentRoadData"
          :terrain-data="terrainData"
          @profile-updated="handleProfileUpdated"
        />
      </div>

      <!-- 路线优化 -->
      <div v-else-if="activeDesignTool === 'optimize'" class="design-section">
        <RouteOptimization
          :road-data="currentRoadData"
          :optimization-params="optimizationParams"
          @optimization-completed="handleOptimizationCompleted"
        />
      </div>

      <!-- 默认状态 -->
      <div v-else class="design-section">
        <el-empty description="请选择设计工具开始道路设计" />
      </div>
    </div>

    <!-- 设计结果面板 -->
    <div class="design-results" v-if="designResults.length > 0">
      <div class="results-header">
        <h4>设计结果</h4>
        <el-button size="small" @click="clearResults">清空</el-button>
      </div>
      <div class="results-list">
        <div
          v-for="(result, index) in designResults"
          :key="index"
          class="result-item"
          :class="{ active: selectedResult === index }"
          @click="selectResult(index)"
        >
          <div class="result-header">
            <el-icon><Route /></el-icon>
            <span class="result-name">{{ result.name }}</span>
            <el-tag :type="getResultStatusType(result.status)" size="small">
              {{ result.status }}
            </el-tag>
          </div>
          <div class="result-details">
            <span>长度: {{ result.length.toFixed(0) }}m</span>
            <span>坡度: {{ result.maxGrade.toFixed(1) }}%</span>
            <span>半径: {{ result.minRadius.toFixed(0) }}m</span>
          </div>
          <div class="result-actions">
            <el-button size="small" @click.stop="viewResult(result)">
              <el-icon><View /></el-icon>
            </el-button>
            <el-button size="small" @click.stop="editResult(result)">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button size="small" type="danger" @click.stop="deleteResult(index)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { 
  Route, 
  TrendCharts, 
  MagicStick, 
  View, 
  Edit, 
  Delete 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { OPENPIT_ROAD_STANDARDS } from '@/config'
import type { RoadData, RoadDesignStandards } from '@/types'

// 临时组件占位符
const RoadAlignmentDesign = { 
  template: '<div class="placeholder">道路选线设计组件</div>',
  props: ['designParams', 'terrainData'],
  emits: ['alignment-created']
}
const RoadProfileDesign = { 
  template: '<div class="placeholder">道路剖面设计组件</div>',
  props: ['roadData', 'terrainData'],
  emits: ['profile-updated']
}
const RouteOptimization = { 
  template: '<div class="placeholder">路线优化组件</div>',
  props: ['roadData', 'optimizationParams'],
  emits: ['optimization-completed']
}

// 响应式数据
const activeDesignTool = ref('')
const selectedResult = ref(-1)

// 设计参数
const designParams = reactive({
  roadType: 'main',
  width: 7,
  designSpeed: 40,
  maxGrade: 8,
  minRadius: 50,
  minSightDistance: 100
})

// 优化参数
const optimizationParams = reactive({
  objectives: {
    minimizeLength: 0.4,
    minimizeEarthwork: 0.4,
    minimizeCost: 0.2
  },
  constraints: {
    maxGrade: 8,
    minRadius: 50,
    avoidAreas: []
  }
})

// 设计结果
const designResults = ref([
  {
    name: '方案一',
    status: 'completed',
    length: 1250,
    maxGrade: 6.5,
    minRadius: 60,
    earthwork: 15000,
    cost: 2500000
  },
  {
    name: '方案二',
    status: 'warning',
    length: 1180,
    maxGrade: 8.2,
    minRadius: 45,
    earthwork: 18000,
    cost: 2800000
  }
])

// 当前道路数据
const currentRoadData = ref<RoadData | null>(null)

// 模拟地形数据
const terrainData = {
  getElevation: (x: number, y: number) => 100 + Math.sin(x * 0.01) * 20 + Math.cos(y * 0.01) * 15,
  getSlope: (x: number, y: number) => Math.random() * 30,
  getAspect: (x: number, y: number) => Math.random() * 360
}

// 计算属性
const currentStandards = computed(() => {
  return OPENPIT_ROAD_STANDARDS[designParams.roadType] || OPENPIT_ROAD_STANDARDS.main
})

// 方法
function setDesignTool(tool: string) {
  activeDesignTool.value = tool
  ElMessage.info(`已切换到${getToolName(tool)}工具`)
}

function getToolName(tool: string): string {
  const names = {
    alignment: '道路选线',
    profile: '道路剖面',
    optimize: '路线优化'
  }
  return names[tool as keyof typeof names] || tool
}

function updateDesignStandards() {
  const standards = currentStandards.value
  designParams.maxGrade = standards.maxGrade
  designParams.minRadius = standards.minRadius
  designParams.minSightDistance = standards.minSightDistance
  designParams.designSpeed = standards.designSpeed
  
  ElMessage.success(`已更新为${getRoadTypeName(designParams.roadType)}设计标准`)
}

function getRoadTypeName(type: string): string {
  const names = {
    main: '主要道路',
    branch: '分支道路',
    access: '通道道路',
    haul: '运输道路'
  }
  return names[type as keyof typeof names] || type
}

function handleAlignmentCreated(alignment: any) {
  ElMessage.success('道路选线创建成功')
  // 处理选线结果
}

function handleProfileUpdated(profile: any) {
  ElMessage.success('道路剖面更新成功')
  // 处理剖面结果
}

function handleOptimizationCompleted(result: any) {
  ElMessage.success('路线优化完成')
  designResults.value.push({
    name: `优化方案${designResults.value.length + 1}`,
    status: 'completed',
    length: result.length || 1200,
    maxGrade: result.maxGrade || 7.0,
    minRadius: result.minRadius || 55,
    earthwork: result.earthwork || 16000,
    cost: result.cost || 2600000
  })
}

function selectResult(index: number) {
  selectedResult.value = index
  const result = designResults.value[index]
  ElMessage.info(`已选择${result.name}`)
}

function viewResult(result: any) {
  ElMessage.info(`查看${result.name}详情`)
  // 实现结果查看逻辑
}

function editResult(result: any) {
  ElMessage.info(`编辑${result.name}`)
  // 实现结果编辑逻辑
}

function deleteResult(index: number) {
  const result = designResults.value[index]
  designResults.value.splice(index, 1)
  if (selectedResult.value === index) {
    selectedResult.value = -1
  }
  ElMessage.success(`已删除${result.name}`)
}

function clearResults() {
  designResults.value = []
  selectedResult.value = -1
  ElMessage.success('已清空所有设计结果')
}

function getResultStatusType(status: string) {
  const types = {
    completed: 'success',
    warning: 'warning',
    error: 'danger',
    processing: 'info'
  }
  return types[status as keyof typeof types] || 'info'
}
</script>

<style scoped>
.road-design-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
}

.design-toolbar {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-background-soft);
  flex-wrap: wrap;
}

.toolbar-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.section-title {
  font-size: var(--font-size-sm);
  font-weight: bold;
  color: var(--color-primary);
}

.param-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.param-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
  white-space: nowrap;
}

.design-content {
  flex: 1;
  overflow: auto;
  padding: var(--spacing-md);
}

.design-section {
  height: 100%;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: var(--color-background-soft);
  border: 2px dashed var(--color-border);
  border-radius: var(--border-radius-base);
  color: var(--color-text-soft);
  font-style: italic;
}

.design-results {
  border-top: 1px solid var(--color-border);
  background-color: var(--color-background-soft);
  max-height: 300px;
  overflow: auto;
}

.results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.results-header h4 {
  margin: 0;
  color: var(--color-primary);
}

.results-list {
  padding: var(--spacing-sm);
}

.result-item {
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-base);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-item:hover {
  background-color: var(--color-background);
  border-color: var(--color-primary);
}

.result-item.active {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-color: var(--color-primary);
}

.result-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.result-name {
  flex: 1;
  font-weight: bold;
  color: var(--color-text);
}

.result-details {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
  margin-bottom: var(--spacing-xs);
}

.result-actions {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
}

/* Element Plus 样式覆盖 */
:deep(.el-select) {
  width: 120px;
}

:deep(.el-input-number) {
  width: 80px;
}

:deep(.el-button-group .el-button) {
  padding: 5px 8px;
}
</style>
