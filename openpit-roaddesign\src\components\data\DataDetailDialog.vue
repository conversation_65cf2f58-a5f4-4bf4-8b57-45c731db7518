<template>
  <div class="data-detail-dialog" v-if="data">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-form
          ref="basicFormRef"
          :model="editForm"
          :rules="rules"
          label-width="120px"
          :disabled="!editMode"
        >
          <el-form-item label="数据名称" prop="name">
            <el-input v-model="editForm.name" />
          </el-form-item>
          <el-form-item label="数据类型">
            <el-tag :type="getDataTypeColor(data.type)">
              {{ getDataTypeName(data.type) }}
            </el-tag>
          </el-form-item>
          <el-form-item label="文件大小">
            <span>{{ data.size }}</span>
          </el-form-item>
          <el-form-item label="坐标系统" prop="coordinateSystem">
            <el-select v-model="editForm.coordinateSystem" style="width: 100%">
              <el-option
                v-for="cs in coordinateSystems"
                :key="cs.code"
                :label="cs.name"
                :value="cs.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <span>{{ formatTime(data.createTime) }}</span>
          </el-form-item>
          <el-form-item label="更新时间" v-if="data.updateTime">
            <span>{{ formatTime(data.updateTime) }}</span>
          </el-form-item>
          <el-form-item label="可见性">
            <el-switch
              v-model="editForm.visible"
              active-text="可见"
              inactive-text="隐藏"
            />
          </el-form-item>
          <el-form-item label="透明度">
            <div class="opacity-control">
              <el-slider
                v-model="editForm.opacity"
                :min="0"
                :max="100"
                style="width: 200px;"
              />
              <span class="opacity-value">{{ editForm.opacity }}%</span>
            </div>
          </el-form-item>
          <el-form-item label="颜色" v-if="data.color">
            <el-color-picker v-model="editForm.color" />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 详细信息 -->
      <el-tab-pane label="详细信息" name="details">
        <div class="metadata-section">
          <template v-if="data.type === 'terrain'">
            <TerrainDetails :data="data" :edit-mode="editMode" />
          </template>
          <template v-else-if="data.type === 'drilling'">
            <DrillingDetails :data="data" :edit-mode="editMode" />
          </template>
          <template v-else-if="data.type === 'geology'">
            <GeologyDetails :data="data" :edit-mode="editMode" />
          </template>
          <template v-else-if="data.type === 'cad'">
            <CADDetails :data="data" :edit-mode="editMode" />
          </template>
          <template v-else-if="data.type === 'road'">
            <RoadDetails :data="data" :edit-mode="editMode" />
          </template>
          <template v-else>
            <el-descriptions :column="2" border>
              <el-descriptions-item
                v-for="(value, key) in data.metadata"
                :key="key"
                :label="key"
              >
                {{ value }}
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </div>
      </el-tab-pane>

      <!-- 预览 -->
      <el-tab-pane label="预览" name="preview">
        <div class="preview-section">
          <div class="preview-toolbar">
            <el-button size="small" @click="refreshPreview">
              <el-icon><Refresh /></el-icon>
              刷新预览
            </el-button>
            <el-button size="small" @click="fullscreenPreview">
              <el-icon><FullScreen /></el-icon>
              全屏预览
            </el-button>
          </div>
          <div class="preview-content">
            <DataPreview :data="data" />
          </div>
        </div>
      </el-tab-pane>

      <!-- 操作历史 -->
      <el-tab-pane label="操作历史" name="history">
        <div class="history-section">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in operationHistory"
              :key="index"
              :timestamp="formatTime(item.timestamp)"
              :type="getHistoryType(item.type)"
            >
              <h4>{{ item.operation }}</h4>
              <p>{{ item.description }}</p>
              <p v-if="item.user">操作人: {{ item.user }}</p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 操作按钮 -->
    <div class="dialog-actions">
      <div class="action-left">
        <el-button @click="exportData">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-button @click="duplicateData">
          <el-icon><CopyDocument /></el-icon>
          复制
        </el-button>
      </div>
      <div class="action-right">
        <el-button @click="$emit('close')">关闭</el-button>
        <el-button v-if="!editMode" type="primary" @click="enableEdit">
          编辑
        </el-button>
        <template v-else>
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" :loading="saving" @click="saveChanges">
            {{ saving ? '保存中...' : '保存' }}
          </el-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Refresh,
  FullScreen,
  Download,
  CopyDocument
} from '@element-plus/icons-vue'
import { DEFAULT_COORDINATE_SYSTEMS } from '@/config'
import type { GISData } from '@/types'

// 临时组件占位符
const TerrainDetails = { template: '<div>地形数据详情</div>' }
const DrillingDetails = { template: '<div>钻孔数据详情</div>' }
const GeologyDetails = { template: '<div>地质数据详情</div>' }
const CADDetails = { template: '<div>CAD数据详情</div>' }
const RoadDetails = { template: '<div>道路数据详情</div>' }
const DataPreview = { template: '<div class="preview-placeholder">数据预览区域</div>' }

interface Props {
  data: GISData | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update-success': [data: GISData]
  'close': []
}>()

// 响应式数据
const activeTab = ref('basic')
const editMode = ref(false)
const saving = ref(false)
const basicFormRef = ref<FormInstance>()

// 编辑表单
const editForm = reactive({
  name: '',
  coordinateSystem: '',
  visible: true,
  opacity: 80,
  color: ''
})

// 操作历史
const operationHistory = ref([
  {
    timestamp: new Date().toISOString(),
    type: 'create',
    operation: '创建数据',
    description: '数据文件已创建',
    user: '系统'
  },
  {
    timestamp: new Date(Date.now() - 3600000).toISOString(),
    type: 'modify',
    operation: '修改属性',
    description: '更新了数据的可见性设置',
    user: '用户'
  }
])

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入数据名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度应在2-50个字符', trigger: 'blur' }
  ],
  coordinateSystem: [
    { required: true, message: '请选择坐标系统', trigger: 'change' }
  ]
}

// 计算属性
const coordinateSystems = computed(() => DEFAULT_COORDINATE_SYSTEMS)

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    resetForm()
  }
}, { immediate: true })

// 方法
function resetForm() {
  if (!props.data) return
  
  editForm.name = props.data.name
  editForm.coordinateSystem = props.data.coordinateSystem
  editForm.visible = props.data.visible
  editForm.opacity = props.data.opacity
  editForm.color = props.data.color || ''
  editMode.value = false
}

function enableEdit() {
  editMode.value = true
}

function cancelEdit() {
  resetForm()
}

async function saveChanges() {
  if (!basicFormRef.value || !props.data) return
  
  try {
    await basicFormRef.value.validate()
    saving.value = true
    
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const updatedData: GISData = {
      ...props.data,
      name: editForm.name,
      coordinateSystem: editForm.coordinateSystem,
      visible: editForm.visible,
      opacity: editForm.opacity,
      color: editForm.color || undefined,
      updateTime: new Date().toISOString()
    }
    
    emit('update-success', updatedData)
    editMode.value = false
    ElMessage.success('数据更新成功')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请检查输入')
  } finally {
    saving.value = false
  }
}

function getDataTypeColor(type: string) {
  const colors = {
    terrain: 'success',
    drilling: 'warning',
    geology: 'danger',
    cad: 'info',
    road: 'primary'
  }
  return colors[type as keyof typeof colors] || 'info'
}

function getDataTypeName(type: string) {
  const names = {
    terrain: '地形数据',
    drilling: '钻孔数据',
    geology: '地质数据',
    cad: 'CAD数据',
    road: '道路数据'
  }
  return names[type as keyof typeof names] || type
}

function formatTime(timeStr: string) {
  return new Date(timeStr).toLocaleString('zh-CN')
}

function getHistoryType(type: string) {
  const types = {
    create: 'primary',
    modify: 'success',
    delete: 'danger',
    export: 'info'
  }
  return types[type as keyof typeof types] || 'info'
}

function refreshPreview() {
  ElMessage.info('预览已刷新')
}

function fullscreenPreview() {
  ElMessage.info('全屏预览功能开发中...')
}

function exportData() {
  if (!props.data) return
  ElMessage.success(`导出数据: ${props.data.name}`)
}

function duplicateData() {
  if (!props.data) return
  ElMessageBox.confirm(
    `确定要复制数据 "${props.data.name}" 吗？`,
    '确认复制',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(() => {
    ElMessage.success('数据复制成功')
  }).catch(() => {
    ElMessage.info('已取消复制')
  })
}
</script>

<style scoped>
.data-detail-dialog {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.el-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow: auto;
}

.opacity-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.opacity-value {
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
  min-width: 40px;
}

.metadata-section,
.preview-section,
.history-section {
  padding: var(--spacing-md);
}

.preview-toolbar {
  margin-bottom: var(--spacing-md);
  display: flex;
  gap: var(--spacing-sm);
}

.preview-content {
  height: 300px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background-soft);
}

.preview-placeholder {
  color: var(--color-text-soft);
  font-style: italic;
}

.dialog-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  background-color: var(--color-background-soft);
}

.action-left,
.action-right {
  display: flex;
  gap: var(--spacing-sm);
}

/* Element Plus 样式覆盖 */
:deep(.el-tabs--border-card) {
  border-color: var(--color-border);
  background-color: var(--color-background);
}

:deep(.el-tabs__header) {
  background-color: var(--color-background-mute);
}

:deep(.el-tabs__item) {
  color: var(--color-text-soft);
}

:deep(.el-tabs__item.is-active) {
  color: var(--color-primary);
  background-color: var(--color-background);
}

:deep(.el-descriptions__label) {
  color: var(--color-text);
  background-color: var(--color-background-soft);
}

:deep(.el-descriptions__content) {
  color: var(--color-text-soft);
}

:deep(.el-timeline-item__timestamp) {
  color: var(--color-text-soft);
}
</style>
