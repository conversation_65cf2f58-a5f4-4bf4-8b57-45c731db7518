<template>
  <div class="safety-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>道路安全检测系统</h2>
      <p class="page-description">
        基于露天矿山道路设计标准，提供全面的道路安全检测、评估和改进建议
      </p>
    </div>

    <!-- 功能模块选择 -->
    <div class="module-selector">
      <el-card>
        <div class="module-tabs">
          <el-tabs v-model="activeModule" @tab-change="handleModuleChange">
            <el-tab-pane label="安全检测" name="check">
              <template #label>
                <div class="tab-label">
                  <el-icon><Search /></el-icon>
                  <span>安全检测</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="风险评估" name="assessment">
              <template #label>
                <div class="tab-label">
                  <el-icon><Warning /></el-icon>
                  <span>风险评估</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="安全监控" name="monitoring">
              <template #label>
                <div class="tab-label">
                  <el-icon><Monitor /></el-icon>
                  <span>安全监控</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="标准管理" name="standards">
              <template #label>
                <div class="tab-label">
                  <el-icon><Document /></el-icon>
                  <span>标准管理</span>
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>

    <!-- 模块内容区 -->
    <div class="module-content">
      <!-- 安全检测模块 -->
      <div v-if="activeModule === 'check'" class="content-panel">
        <SafetyCheckPanel />
      </div>

      <!-- 风险评估模块 -->
      <div v-else-if="activeModule === 'assessment'" class="content-panel">
        <RiskAssessmentPanel />
      </div>

      <!-- 安全监控模块 -->
      <div v-else-if="activeModule === 'monitoring'" class="content-panel">
        <SafetyMonitoringPanel />
      </div>

      <!-- 标准管理模块 -->
      <div v-else-if="activeModule === 'standards'" class="content-panel">
        <SafetyStandardsPanel />
      </div>
    </div>

    <!-- 安全状态概览 -->
    <div class="safety-overview" v-if="safetyOverview">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>安全状态概览</span>
            <el-button size="small" @click="refreshOverview">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </template>

        <div class="overview-grid">
          <div class="overview-item">
            <div class="overview-icon safe">
              <el-icon><SuccessFilled /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-number">{{ safetyOverview.safeRoads }}</div>
              <div class="overview-label">安全道路</div>
            </div>
          </div>

          <div class="overview-item">
            <div class="overview-icon warning">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-number">{{ safetyOverview.warningRoads }}</div>
              <div class="overview-label">警告道路</div>
            </div>
          </div>

          <div class="overview-item">
            <div class="overview-icon danger">
              <el-icon><CircleCloseFilled /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-number">{{ safetyOverview.dangerRoads }}</div>
              <div class="overview-label">危险道路</div>
            </div>
          </div>

          <div class="overview-item">
            <div class="overview-icon info">
              <el-icon><InfoFilled /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-number">{{ safetyOverview.totalRoads }}</div>
              <div class="overview-label">总计道路</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Operation /></el-icon>
            <span>快速操作</span>
          </div>
        </template>

        <div class="action-grid">
          <el-button class="action-button" @click="quickSafetyCheck">
            <div class="action-content">
              <el-icon size="24"><Search /></el-icon>
              <span>快速检测</span>
            </div>
          </el-button>

          <el-button class="action-button" @click="batchAssessment">
            <div class="action-content">
              <el-icon size="24"><Warning /></el-icon>
              <span>批量评估</span>
            </div>
          </el-button>

          <el-button class="action-button" @click="generateSafetyReport">
            <div class="action-content">
              <el-icon size="24"><Document /></el-icon>
              <span>生成报告</span>
            </div>
          </el-button>

          <el-button class="action-button" @click="exportSafetyData">
            <div class="action-content">
              <el-icon size="24"><Download /></el-icon>
              <span>导出数据</span>
            </div>
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  Search,
  Warning,
  Monitor,
  Document,
  DataAnalysis,
  Refresh,
  SuccessFilled,
  CircleCloseFilled,
  InfoFilled,
  Operation,
  Download
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import SafetyCheckPanel from '@/components/safety/SafetyCheckPanel.vue'

// 临时组件占位符
const RiskAssessmentPanel = {
  template: '<div class="placeholder">风险评估模块开发中...</div>'
}
const SafetyMonitoringPanel = {
  template: '<div class="placeholder">安全监控模块开发中...</div>'
}
const SafetyStandardsPanel = {
  template: '<div class="placeholder">标准管理模块开发中...</div>'
}

// 响应式数据
const activeModule = ref('check')

// 安全状态概览
const safetyOverview = reactive({
  safeRoads: 8,
  warningRoads: 3,
  dangerRoads: 1,
  totalRoads: 12,
  lastUpdate: new Date()
})

// 方法
function handleModuleChange(moduleName: string) {
  ElMessage.info(`已切换到${getModuleName(moduleName)}模块`)
}

function getModuleName(module: string): string {
  const names = {
    check: '安全检测',
    assessment: '风险评估',
    monitoring: '安全监控',
    standards: '标准管理'
  }
  return names[module as keyof typeof names] || module
}

function refreshOverview() {
  // 刷新安全状态概览
  safetyOverview.lastUpdate = new Date()
  ElMessage.success('安全状态概览已刷新')
}

function quickSafetyCheck() {
  ElMessage.info('快速安全检测功能开发中...')
}

function batchAssessment() {
  ElMessage.info('批量风险评估功能开发中...')
}

function generateSafetyReport() {
  ElMessage.info('生成安全报告功能开发中...')
}

function exportSafetyData() {
  ElMessage.info('导出安全数据功能开发中...')
}

// 生命周期
onMounted(() => {
  // 初始化安全概览数据
  refreshOverview()
})
</script>

<style scoped>
.safety-view {
  padding: var(--spacing-lg);
  color: var(--color-text);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.page-header {
  margin-bottom: var(--spacing-md);
}

.page-header h2 {
  color: var(--color-primary);
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-2xl);
}

.page-description {
  color: var(--color-text-soft);
  margin: 0;
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.module-selector {
  flex-shrink: 0;
}

.module-tabs {
  margin: -20px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.module-content {
  flex: 1;
  overflow: hidden;
}

.content-panel {
  height: 100%;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  background-color: var(--color-background-soft);
  border: 2px dashed var(--color-border);
  border-radius: var(--border-radius-base);
  color: var(--color-text-soft);
  font-style: italic;
  font-size: var(--font-size-lg);
}

.safety-overview {
  flex-shrink: 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.overview-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--color-background);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--color-border);
}

.overview-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.overview-icon.safe {
  background-color: rgba(103, 194, 58, 0.1);
  color: var(--color-success);
}

.overview-icon.warning {
  background-color: rgba(230, 162, 60, 0.1);
  color: var(--color-warning);
}

.overview-icon.danger {
  background-color: rgba(245, 108, 108, 0.1);
  color: var(--color-danger);
}

.overview-icon.info {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  color: var(--color-primary);
}

.overview-content {
  flex: 1;
}

.overview-number {
  font-size: var(--font-size-2xl);
  font-weight: bold;
  color: var(--color-text);
}

.overview-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
  margin-top: var(--spacing-xs);
}

.quick-actions {
  flex-shrink: 0;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.action-button {
  height: 80px;
  border: 2px dashed var(--color-border);
  background-color: transparent;
  transition: all 0.3s ease;
}

.action-button:hover {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.action-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-text-soft);
}

.action-button:hover .action-content {
  color: var(--color-primary);
}

/* Element Plus 样式覆盖 */
:deep(.el-tabs__header) {
  margin: 0;
  background-color: var(--color-background-mute);
  border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 var(--spacing-md);
}

:deep(.el-tabs__item) {
  color: var(--color-text-soft);
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: var(--color-primary);
}

:deep(.el-tabs__active-bar) {
  background-color: var(--color-primary);
}

:deep(.el-tabs__content) {
  padding: var(--spacing-lg);
}

:deep(.el-card) {
  background-color: var(--color-background-soft);
  border-color: var(--color-border);
}
</style>
