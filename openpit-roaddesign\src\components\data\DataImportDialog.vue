<template>
  <div class="data-import-dialog">
    <!-- 导入方式选择 -->
    <div class="import-method">
      <el-radio-group v-model="importMethod">
        <el-radio label="file">文件导入</el-radio>
        <el-radio label="url">URL导入</el-radio>
        <el-radio label="generate">生成示例数据</el-radio>
      </el-radio-group>
    </div>

    <!-- 文件导入 -->
    <div v-if="importMethod === 'file'" class="file-import">
      <el-upload
        ref="uploadRef"
        class="upload-area"
        drag
        :multiple="true"
        :auto-upload="false"
        :accept="acceptedFileTypes"
        :on-change="handleFileChange"
        :file-list="fileList"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持的文件格式: {{ acceptedFileTypes }}
          </div>
        </template>
      </el-upload>
    </div>

    <!-- URL导入 -->
    <div v-if="importMethod === 'url'" class="url-import">
      <el-form :model="urlForm" label-width="100px">
        <el-form-item label="数据URL">
          <el-input
            v-model="urlForm.url"
            placeholder="请输入数据文件的URL地址"
            clearable
          />
        </el-form-item>
        <el-form-item label="认证信息">
          <el-input
            v-model="urlForm.auth"
            placeholder="如需要，请输入认证信息"
            type="password"
            clearable
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 生成示例数据 -->
    <div v-if="importMethod === 'generate'" class="generate-data">
      <el-form :model="generateForm" label-width="100px">
        <el-form-item label="数据数量">
          <el-input-number
            v-model="generateForm.count"
            :min="1"
            :max="100"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="区域范围">
          <div class="coordinate-inputs">
            <el-input
              v-model="generateForm.bounds.minLon"
              placeholder="最小经度"
              size="small"
            />
            <el-input
              v-model="generateForm.bounds.minLat"
              placeholder="最小纬度"
              size="small"
            />
            <el-input
              v-model="generateForm.bounds.maxLon"
              placeholder="最大经度"
              size="small"
            />
            <el-input
              v-model="generateForm.bounds.maxLat"
              placeholder="最大纬度"
              size="small"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 导入配置 -->
    <div class="import-config">
      <el-form :model="configForm" label-width="100px">
        <el-form-item label="坐标系统">
          <el-select v-model="configForm.coordinateSystem" style="width: 100%">
            <el-option
              v-for="cs in coordinateSystems"
              :key="cs.code"
              :label="cs.name"
              :value="cs.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据名称">
          <el-input
            v-model="configForm.name"
            placeholder="自动生成或手动输入"
            clearable
          />
        </el-form-item>
        <el-form-item label="描述信息">
          <el-input
            v-model="configForm.description"
            type="textarea"
            :rows="3"
            placeholder="可选的描述信息"
          />
        </el-form-item>
      </el-form>
    </div>

    <!-- 进度条 -->
    <div v-if="importing" class="import-progress">
      <el-progress
        :percentage="importProgress"
        :status="importStatus"
        :stroke-width="8"
      />
      <p class="progress-text">{{ importProgressText }}</p>
    </div>

    <!-- 操作按钮 -->
    <div class="dialog-actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button
        type="primary"
        :loading="importing"
        :disabled="!canImport"
        @click="handleImport"
      >
        {{ importing ? '导入中...' : '开始导入' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { UploadFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { UploadFile } from 'element-plus'
import { FILE_TYPES, DEFAULT_COORDINATE_SYSTEMS } from '@/config'
import type { GISData } from '@/types'

interface Props {
  dataType: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'import-success': [data: GISData[]]
  'cancel': []
}>()

// 响应式数据
const importMethod = ref('file')
const fileList = ref<UploadFile[]>([])
const importing = ref(false)
const importProgress = ref(0)
const importStatus = ref<'success' | 'exception' | undefined>()
const importProgressText = ref('')

// 表单数据
const urlForm = ref({
  url: '',
  auth: ''
})

const generateForm = ref({
  count: 10,
  bounds: {
    minLon: 116.3,
    minLat: 39.8,
    maxLon: 116.5,
    maxLat: 40.0
  }
})

const configForm = ref({
  coordinateSystem: 'EPSG:4326',
  name: '',
  description: ''
})

// 计算属性
const acceptedFileTypes = computed(() => {
  const types = FILE_TYPES[props.dataType as keyof typeof FILE_TYPES]
  return types ? types.extensions.join(',') : '*'
})

const coordinateSystems = computed(() => DEFAULT_COORDINATE_SYSTEMS)

const canImport = computed(() => {
  switch (importMethod.value) {
    case 'file':
      return fileList.value.length > 0
    case 'url':
      return urlForm.value.url.trim() !== ''
    case 'generate':
      return generateForm.value.count > 0
    default:
      return false
  }
})

// 监听数据类型变化
watch(() => props.dataType, (newType) => {
  // 重置表单
  fileList.value = []
  configForm.value.name = ''
  configForm.value.description = ''
})

// 方法
function handleFileChange(file: UploadFile, files: UploadFile[]) {
  fileList.value = files
  
  // 自动生成名称
  if (files.length === 1 && !configForm.value.name) {
    const fileName = file.name?.split('.')[0] || ''
    configForm.value.name = fileName
  } else if (files.length > 1) {
    configForm.value.name = `批量导入_${files.length}个文件`
  }
}

async function handleImport() {
  importing.value = true
  importProgress.value = 0
  importStatus.value = undefined
  
  try {
    let importedData: GISData[] = []
    
    switch (importMethod.value) {
      case 'file':
        importedData = await importFromFiles()
        break
      case 'url':
        importedData = await importFromUrl()
        break
      case 'generate':
        importedData = await generateSampleData()
        break
    }
    
    importProgress.value = 100
    importStatus.value = 'success'
    importProgressText.value = '导入完成'
    
    emit('import-success', importedData)
  } catch (error) {
    console.error('导入失败:', error)
    importStatus.value = 'exception'
    importProgressText.value = '导入失败'
    ElMessage.error('导入失败: ' + (error as Error).message)
  } finally {
    importing.value = false
  }
}

async function importFromFiles(): Promise<GISData[]> {
  const results: GISData[] = []
  
  for (let i = 0; i < fileList.value.length; i++) {
    const file = fileList.value[i]
    importProgressText.value = `正在处理文件: ${file.name}`
    importProgress.value = (i / fileList.value.length) * 80
    
    // 模拟文件处理
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const data: GISData = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      name: configForm.value.name || file.name?.split('.')[0] || '未命名',
      type: props.dataType as any,
      size: formatFileSize(file.size || 0),
      createTime: new Date().toISOString(),
      visible: true,
      opacity: 80,
      coordinateSystem: configForm.value.coordinateSystem,
      filePath: URL.createObjectURL(file.raw!),
      metadata: {
        originalName: file.name,
        description: configForm.value.description
      }
    }
    
    results.push(data)
  }
  
  return results
}

async function importFromUrl(): Promise<GISData[]> {
  importProgressText.value = '正在从URL获取数据...'
  importProgress.value = 30
  
  // 模拟URL数据获取
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  importProgress.value = 80
  importProgressText.value = '正在解析数据...'
  
  const data: GISData = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    name: configForm.value.name || 'URL导入数据',
    type: props.dataType as any,
    size: '未知',
    createTime: new Date().toISOString(),
    visible: true,
    opacity: 80,
    coordinateSystem: configForm.value.coordinateSystem,
    filePath: urlForm.value.url,
    metadata: {
      source: 'url',
      url: urlForm.value.url,
      description: configForm.value.description
    }
  }
  
  return [data]
}

async function generateSampleData(): Promise<GISData[]> {
  const results: GISData[] = []
  const { count, bounds } = generateForm.value
  
  for (let i = 0; i < count; i++) {
    importProgressText.value = `正在生成示例数据 ${i + 1}/${count}`
    importProgress.value = (i / count) * 80
    
    // 模拟数据生成
    await new Promise(resolve => setTimeout(resolve, 100))
    
    const data: GISData = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      name: `${getDataTypeName(props.dataType)}_${i + 1}`,
      type: props.dataType as any,
      size: `${Math.floor(Math.random() * 10 + 1)}.${Math.floor(Math.random() * 9)}MB`,
      createTime: new Date().toISOString(),
      visible: true,
      opacity: 80,
      coordinateSystem: configForm.value.coordinateSystem,
      metadata: {
        generated: true,
        bounds: bounds,
        description: configForm.value.description || '自动生成的示例数据'
      }
    }
    
    results.push(data)
  }
  
  return results
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

function getDataTypeName(type: string): string {
  const names = {
    terrain: '地形数据',
    drilling: '钻孔数据',
    geology: '地质数据',
    cad: 'CAD数据',
    road: '道路数据'
  }
  return names[type as keyof typeof names] || type
}
</script>

<style scoped>
.data-import-dialog {
  padding: var(--spacing-md);
}

.import-method {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.file-import,
.url-import,
.generate-data {
  margin-bottom: var(--spacing-lg);
}

.upload-area {
  width: 100%;
}

.coordinate-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm);
}

.import-config {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--color-background-soft);
  border-radius: var(--border-radius-base);
}

.import-progress {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--color-background-soft);
  border-radius: var(--border-radius-base);
}

.progress-text {
  margin-top: var(--spacing-sm);
  text-align: center;
  color: var(--color-text-soft);
  font-size: var(--font-size-sm);
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

/* Element Plus 样式覆盖 */
:deep(.el-upload-dragger) {
  background-color: var(--color-background);
  border-color: var(--color-border);
}

:deep(.el-upload-dragger:hover) {
  border-color: var(--color-primary);
}

:deep(.el-upload__tip) {
  color: var(--color-text-soft);
}
</style>
