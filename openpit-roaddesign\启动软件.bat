@echo off
chcp 65001 >nul
title 露天矿山道路设计软件 - 快速启动

:: 获取脚本所在目录并切换到项目目录
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

echo.
echo ========================================
echo    露天矿山道路设计软件启动器 v1.0
echo ========================================
echo.
echo 🚀 启动露天矿山道路设计软件...
echo 📁 项目目录: %SCRIPT_DIR%
echo.

:: 检查Node.js
echo [1/5] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 请先安装Node.js: https://nodejs.org/
    echo.
    echo 💡 安装步骤:
    echo    1. 访问 https://nodejs.org/
    echo    2. 下载LTS版本
    echo    3. 安装后重启命令行
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

:: 检查npm
echo [2/5] 检查npm包管理器...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm不可用
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: %NPM_VERSION%

:: 检查项目文件
echo [3/5] 检查项目文件...
if not exist "package.json" (
    echo ❌ 未找到package.json文件
    echo    请确保在项目根目录运行此脚本
    echo    当前目录: %CD%
    pause
    exit /b 1
)

if not exist "src" (
    echo ❌ 未找到src目录
    echo    项目文件不完整
    pause
    exit /b 1
)

echo ✅ 项目文件检查通过

:: 检查依赖
echo [4/5] 检查项目依赖...
if not exist "node_modules" (
    echo ⚠️  未检测到node_modules目录
    echo 📦 正在安装依赖，这可能需要几分钟...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        echo.
        echo 💡 可能的解决方案:
        echo    1. 检查网络连接
        echo    2. 尝试使用淘宝镜像: npm config set registry https://registry.npmmirror.com
        echo    3. 清理缓存: npm cache clean --force
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

:: 启动开发服务器
echo [5/5] 启动开发服务器...
echo.
echo 🌐 正在启动开发服务器...
echo    ┌─────────────────────────────────────┐
echo    │  服务器地址: http://localhost:3000  │
echo    │  按 Ctrl+C 可停止服务器             │
echo    └─────────────────────────────────────┘
echo.

:: 设置环境变量
set NODE_ENV=development
set VITE_DEBUG_MODE=true

:: 启动服务器
npm run dev

:: 服务器停止后的提示
echo.
echo ⚠️  开发服务器已停止
echo.
echo 💡 如果遇到问题，请:
echo    1. 检查端口3000是否被占用
echo    2. 查看上方的错误信息
echo    3. 尝试重新运行此脚本
echo.
pause
