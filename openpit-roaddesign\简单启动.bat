@echo off
chcp 65001 >nul
title 露天矿山道路设计软件

echo 🚀 启动露天矿山道路设计软件...
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"

echo 📁 当前目录: %CD%
echo.

:: 检查基本文件
if not exist "package.json" (
    echo ❌ 错误: 未找到package.json文件
    echo 当前目录: %CD%
    pause
    exit /b 1
)

echo ✅ 项目文件检查通过
echo.

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js环境正常
echo.

:: 检查依赖
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查完成
echo.

:: 启动服务器
echo 🌐 启动开发服务器...
echo 访问地址: http://localhost:3000
echo 按 Ctrl+C 停止服务器
echo.

npm run dev

echo.
echo 服务器已停止
pause
