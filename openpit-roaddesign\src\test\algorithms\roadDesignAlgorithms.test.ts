/**
 * 道路设计算法单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { RoadAlignmentAlgorithm, RoadConflictDetection, RouteOptimization, RoadProfileGeneration } from '@/utils/roadDesignAlgorithms'
import { createMockRoadData } from '@/test/utils'
import type { Point3D, TerrainData } from '@/utils/roadDesignAlgorithms'

// 模拟地形数据
const mockTerrainData: TerrainData = {
  getElevation: vi.fn((x: number, y: number) => 50 + Math.sin(x * 0.01) * 10),
  getSlope: vi.fn((x: number, y: number) => Math.random() * 30),
  getAspect: vi.fn((x: number, y: number) => Math.random() * 360)
}

describe('RoadAlignmentAlgorithm', () => {
  let algorithm: RoadAlignmentAlgorithm

  beforeEach(() => {
    algorithm = new RoadAlignmentAlgorithm(mockTerrainData, 'main')
  })

  describe('generateAlignment', () => {
    it('应该生成有效的道路选线', () => {
      const startPoint: Point3D = { x: 116.3974, y: 39.9093, z: 50 }
      const endPoint: Point3D = { x: 116.4074, y: 39.9193, z: 80 }
      const waypoints: Point3D[] = [
        { x: 116.4024, y: 39.9143, z: 65 }
      ]

      const result = algorithm.generateAlignment(startPoint, endPoint, waypoints)

      expect(result).toBeDefined()
      expect(result.points).toHaveLength.greaterThan(0)
      expect(result.totalLength).toBeGreaterThan(0)
      expect(result.horizontalCurves).toBeDefined()
      expect(result.verticalCurves).toBeDefined()
    })

    it('应该处理空控制点数组', () => {
      const startPoint: Point3D = { x: 116.3974, y: 39.9093, z: 50 }
      const endPoint: Point3D = { x: 116.4074, y: 39.9193, z: 80 }

      const result = algorithm.generateAlignment(startPoint, endPoint, [])

      expect(result).toBeDefined()
      expect(result.points).toHaveLength.greaterThan(0)
    })

    it('应该生成合理的总长度', () => {
      const startPoint: Point3D = { x: 116.3974, y: 39.9093, z: 50 }
      const endPoint: Point3D = { x: 116.4074, y: 39.9193, z: 80 }

      const result = algorithm.generateAlignment(startPoint, endPoint)

      // 总长度应该大于直线距离
      const directDistance = Math.sqrt(
        Math.pow(endPoint.x - startPoint.x, 2) + 
        Math.pow(endPoint.y - startPoint.y, 2)
      ) * 111320 // 近似转换为米

      expect(result.totalLength).toBeGreaterThanOrEqual(directDistance * 0.8)
    })
  })

  describe('地形适应性', () => {
    it('应该调用地形数据获取高程', () => {
      const startPoint: Point3D = { x: 116.3974, y: 39.9093, z: 50 }
      const endPoint: Point3D = { x: 116.4074, y: 39.9193, z: 80 }

      algorithm.generateAlignment(startPoint, endPoint)

      expect(mockTerrainData.getElevation).toHaveBeenCalled()
    })
  })
})

describe('RoadConflictDetection', () => {
  describe('detectTerrainConflicts', () => {
    it('应该检测地形冲突', () => {
      const roadData = createMockRoadData()
      
      const conflicts = RoadConflictDetection.detectTerrainConflicts(roadData, mockTerrainData)

      expect(conflicts).toBeDefined()
      expect(Array.isArray(conflicts)).toBe(true)
    })

    it('应该识别填挖高度过大的问题', () => {
      const roadData = createMockRoadData({
        centerline: {
          points: [
            { longitude: 116.3974, latitude: 39.9093, elevation: 100, station: 0 }, // 高程差异很大
            { longitude: 116.3984, latitude: 39.9103, elevation: 105, station: 100 }
          ],
          totalLength: 100,
          horizontalCurves: [],
          verticalCurves: []
        }
      })

      const conflicts = RoadConflictDetection.detectTerrainConflicts(roadData, mockTerrainData)

      expect(conflicts.length).toBeGreaterThan(0)
      expect(conflicts[0].type).toBe('grade')
    })
  })

  describe('detectRoadConflicts', () => {
    it('应该检测道路间冲突', () => {
      const road1 = createMockRoadData({ id: 'road-1', name: '道路1' })
      const road2 = createMockRoadData({ id: 'road-2', name: '道路2' })

      const conflicts = RoadConflictDetection.detectRoadConflicts([road1, road2])

      expect(conflicts).toBeDefined()
      expect(Array.isArray(conflicts)).toBe(true)
    })
  })
})

describe('RouteOptimization', () => {
  describe('optimizeRoute', () => {
    it('应该优化运输路线', () => {
      const params = {
        startPoint: [116.3974, 39.9093, 50] as [number, number, number],
        endPoint: [116.4074, 39.9193, 80] as [number, number, number],
        waypoints: [
          [116.4024, 39.9143, 65] as [number, number, number]
        ],
        constraints: {
          avoidAreas: []
        },
        objectives: {
          minimizeLength: 0.4,
          minimizeEarthwork: 0.4,
          minimizeCost: 0.2
        }
      }

      const result = RouteOptimization.optimizeRoute(params)

      expect(result).toBeDefined()
      expect(result.points).toHaveLength.greaterThan(0)
      expect(result.totalLength).toBeGreaterThan(0)
    })

    it('应该处理避让区域约束', () => {
      const params = {
        startPoint: [116.3974, 39.9093, 50] as [number, number, number],
        endPoint: [116.4074, 39.9193, 80] as [number, number, number],
        waypoints: [],
        constraints: {
          avoidAreas: [
            {
              coordinates: [[
                [116.4000, 39.9100],
                [116.4050, 39.9100],
                [116.4050, 39.9150],
                [116.4000, 39.9150],
                [116.4000, 39.9100]
              ]]
            }
          ]
        },
        objectives: {
          minimizeLength: 1.0,
          minimizeEarthwork: 0.0,
          minimizeCost: 0.0
        }
      }

      const result = RouteOptimization.optimizeRoute(params)

      expect(result).toBeDefined()
      expect(result.points).toHaveLength.greaterThan(0)
    })
  })
})

describe('RoadProfileGeneration', () => {
  describe('generateLongitudinalProfile', () => {
    it('应该生成纵断面数据', () => {
      const roadData = createMockRoadData()
      
      const profile = RoadProfileGeneration.generateLongitudinalProfile(
        roadData.centerline,
        mockTerrainData
      )

      expect(profile).toBeDefined()
      expect(profile.length).toBe(roadData.centerline.points.length)
      
      profile.forEach(point => {
        expect(point).toHaveProperty('station')
        expect(point).toHaveProperty('roadElevation')
        expect(point).toHaveProperty('terrainElevation')
        expect(point).toHaveProperty('cutFill')
        expect(point).toHaveProperty('grade')
      })
    })

    it('应该计算正确的坡度', () => {
      const roadData = createMockRoadData({
        centerline: {
          points: [
            { longitude: 116.3974, latitude: 39.9093, elevation: 50, station: 0 },
            { longitude: 116.3984, latitude: 39.9103, elevation: 60, station: 100 }, // 10米高差，100米距离 = 10%坡度
            { longitude: 116.3994, latitude: 39.9113, elevation: 65, station: 200 }  // 5米高差，100米距离 = 5%坡度
          ],
          totalLength: 200,
          horizontalCurves: [],
          verticalCurves: []
        }
      })

      const profile = RoadProfileGeneration.generateLongitudinalProfile(
        roadData.centerline,
        mockTerrainData
      )

      expect(profile[1].grade).toBeCloseTo(10, 1) // 第二个点的坡度应该接近10%
      expect(profile[2].grade).toBeCloseTo(5, 1)  // 第三个点的坡度应该接近5%
    })
  })

  describe('generateCrossSections', () => {
    it('应该生成横断面数据', () => {
      const roadData = createMockRoadData()
      const roadWidth = 7
      const interval = 20

      const crossSections = RoadProfileGeneration.generateCrossSections(
        roadData.centerline,
        roadWidth,
        mockTerrainData,
        interval
      )

      expect(crossSections).toBeDefined()
      expect(crossSections.length).toBeGreaterThan(0)
      
      crossSections.forEach(section => {
        expect(section).toHaveProperty('station')
        expect(section).toHaveProperty('points')
        expect(section).toHaveProperty('leftSlope')
        expect(section).toHaveProperty('rightSlope')
        expect(section).toHaveProperty('cutFill')
        expect(section).toHaveProperty('volume')
      })
    })

    it('应该生成正确数量的横断面', () => {
      const roadData = createMockRoadData()
      const interval = 50

      const crossSections = RoadProfileGeneration.generateCrossSections(
        roadData.centerline,
        7,
        mockTerrainData,
        interval
      )

      const expectedCount = Math.ceil(roadData.centerline.points.length / (interval / 10))
      expect(crossSections.length).toBeLessThanOrEqual(expectedCount)
    })
  })
})

describe('算法性能测试', () => {
  it('道路选线算法应该在合理时间内完成', async () => {
    const algorithm = new RoadAlignmentAlgorithm(mockTerrainData, 'main')
    const startPoint: Point3D = { x: 116.3974, y: 39.9093, z: 50 }
    const endPoint: Point3D = { x: 116.4074, y: 39.9193, z: 80 }

    const startTime = performance.now()
    algorithm.generateAlignment(startPoint, endPoint)
    const endTime = performance.now()

    const executionTime = endTime - startTime
    expect(executionTime).toBeLessThan(1000) // 应该在1秒内完成
  })

  it('冲突检测算法应该高效处理大量数据', () => {
    // 创建包含大量点的道路数据
    const points = []
    for (let i = 0; i < 1000; i++) {
      points.push({
        longitude: 116.3974 + i * 0.0001,
        latitude: 39.9093 + i * 0.0001,
        elevation: 50 + Math.sin(i * 0.01) * 10,
        station: i * 10
      })
    }

    const roadData = createMockRoadData({
      centerline: {
        points,
        totalLength: 10000,
        horizontalCurves: [],
        verticalCurves: []
      }
    })

    const startTime = performance.now()
    const conflicts = RoadConflictDetection.detectTerrainConflicts(roadData, mockTerrainData)
    const endTime = performance.now()

    const executionTime = endTime - startTime
    expect(executionTime).toBeLessThan(2000) // 应该在2秒内完成
    expect(conflicts).toBeDefined()
  })
})
