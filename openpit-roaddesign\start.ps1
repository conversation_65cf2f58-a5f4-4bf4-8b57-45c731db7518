# 露天矿山道路设计软件启动器 - PowerShell版本

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Blue
Write-Host "   露天矿山道路设计软件启动器 v1.0" -ForegroundColor Blue
Write-Host "========================================" -ForegroundColor Blue
Write-Host ""

# 获取脚本所在目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "📁 项目目录: $ScriptDir" -ForegroundColor Yellow
Write-Host ""

# 检查Node.js
Write-Host "[1/6] 检查Node.js环境..." -ForegroundColor Cyan
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未检测到Node.js，请先安装Node.js" -ForegroundColor Red
    Write-Host "   下载地址: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查npm
Write-Host "[2/6] 检查npm包管理器..." -ForegroundColor Cyan
try {
    $npmVersion = npm --version
    Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: npm不可用" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查项目文件
Write-Host "[3/6] 检查项目文件..." -ForegroundColor Cyan
if (-not (Test-Path "package.json")) {
    Write-Host "❌ 错误: 未找到package.json文件，请确保在项目根目录运行此脚本" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

if (-not (Test-Path "src")) {
    Write-Host "❌ 错误: 未找到src目录，项目文件不完整" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "✅ 项目文件检查通过" -ForegroundColor Green

# 检查依赖
Write-Host "[4/6] 检查项目依赖..." -ForegroundColor Cyan
if (-not (Test-Path "node_modules")) {
    Write-Host "⚠️  未检测到node_modules目录，正在安装依赖..." -ForegroundColor Yellow
    Write-Host "   这可能需要几分钟时间，请耐心等待..." -ForegroundColor Yellow
    
    try {
        npm install
        Write-Host "✅ 依赖安装完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 错误: 依赖安装失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
} else {
    Write-Host "✅ 依赖已安装" -ForegroundColor Green
}

# 环境变量检查
Write-Host "[5/6] 检查环境配置..." -ForegroundColor Cyan
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  未找到.env文件，使用默认配置" -ForegroundColor Yellow
} else {
    Write-Host "✅ 环境配置文件存在" -ForegroundColor Green
}

# 启动开发服务器
Write-Host "[6/6] 启动开发服务器..." -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 正在启动露天矿山道路设计软件..." -ForegroundColor Green
Write-Host "   - 服务器地址: http://localhost:3000" -ForegroundColor Yellow
Write-Host "   - 按 Ctrl+C 可停止服务器" -ForegroundColor Yellow
Write-Host ""

# 设置环境变量
$env:NODE_ENV = "development"
$env:VITE_DEBUG_MODE = "true"

# 启动服务器
try {
    npm run dev
} catch {
    Write-Host ""
    Write-Host "⚠️  开发服务器启动失败或已停止" -ForegroundColor Yellow
    Write-Host "   请检查错误信息并重试" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "按任意键退出"
