/**
 * 模块集成服务
 * 处理各功能模块间的具体数据传递和业务逻辑集成
 */

import { ref, reactive, computed } from 'vue'
import { dataFlowManager, emitDataFlowEvent, onDataFlowEvent } from '@/utils/dataFlowManager'
import { RoadAlignmentAlgorithm } from '@/utils/roadDesignAlgorithms'
import { RoadSafetyChecker } from '@/utils/safetyCheckAlgorithms'
import { useGISStore } from '@/stores/gis'
import { useAppStore } from '@/stores/counter'
import type { 
  GISData, 
  RoadData, 
  SafetyCheckResult, 
  RoadCenterline,
  DataFlowEvent 
} from '@/types'

/**
 * 模块集成服务类
 */
export class ModuleIntegrationService {
  private static instance: ModuleIntegrationService
  private gisStore = useGISStore()
  private appStore = useAppStore()
  
  // 集成状态
  private integrationState = reactive({
    isInitialized: false,
    activeConnections: new Set<string>(),
    dataSync: {
      gisToRoad: true,
      roadToSafety: true,
      safetyToReport: true,
      dataToGis: true
    },
    lastSyncTime: 0
  })

  private constructor() {
    this.initialize()
  }

  public static getInstance(): ModuleIntegrationService {
    if (!ModuleIntegrationService.instance) {
      ModuleIntegrationService.instance = new ModuleIntegrationService()
    }
    return ModuleIntegrationService.instance
  }

  /**
   * 初始化集成服务
   */
  private initialize() {
    this.setupEventHandlers()
    this.setupDataSyncRules()
    this.integrationState.isInitialized = true
    console.log('模块集成服务已初始化')
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers() {
    // 处理数据导入事件
    onDataFlowEvent('data_imported', (event) => {
      this.handleDataImported(event)
    })

    // 处理道路设计事件
    onDataFlowEvent('road_designed', (event) => {
      this.handleRoadDesigned(event)
    })

    // 处理安全检测事件
    onDataFlowEvent('safety_checked', (event) => {
      this.handleSafetyChecked(event)
    })

    // 处理模块切换事件
    onDataFlowEvent('module_switched', (event) => {
      this.handleModuleSwitched(event)
    })

    // 处理选择变化事件
    onDataFlowEvent('selection_changed', (event) => {
      this.handleSelectionChanged(event)
    })
  }

  /**
   * 设置数据同步规则
   */
  private setupDataSyncRules() {
    // GIS数据变化时自动同步到相关模块
    this.gisStore.$subscribe((mutation, state) => {
      if (this.integrationState.dataSync.gisToRoad) {
        this.syncGISDataToRoadModule()
      }
      this.integrationState.lastSyncTime = Date.now()
    })
  }

  /**
   * 处理数据导入事件
   */
  private handleDataImported(event: DataFlowEvent) {
    const { data, source } = event
    
    console.log(`处理数据导入事件: ${source}`, data)

    // 根据数据类型进行不同处理
    if (data.type === 'road') {
      this.processRoadDataImport(data)
    } else if (data.type === 'terrain') {
      this.processTerrainDataImport(data)
    } else if (data.type === 'drilling') {
      this.processDrillingDataImport(data)
    }

    // 通知相关模块数据已更新
    emitDataFlowEvent('data_updated', source, data, 'all')
  }

  /**
   * 处理道路设计事件
   */
  private handleRoadDesigned(event: DataFlowEvent) {
    const { data } = event
    const roadData = data as RoadData

    console.log('处理道路设计事件', roadData)

    // 将道路设计结果添加到GIS数据中
    this.addRoadDataToGIS(roadData)

    // 缓存道路设计结果
    dataFlowManager.setCachedData('latest_road_design', roadData)

    // 如果启用了自动安全检测，触发安全检测
    if (this.integrationState.dataSync.roadToSafety) {
      this.triggerAutomaticSafetyCheck(roadData)
    }

    // 通知报告模块
    emitDataFlowEvent('data_updated', 'road', roadData, 'report')
  }

  /**
   * 处理安全检测事件
   */
  private handleSafetyChecked(event: DataFlowEvent) {
    const { data } = event
    const safetyResults = data as SafetyCheckResult[]

    console.log('处理安全检测事件', safetyResults)

    // 缓存安全检测结果
    dataFlowManager.setCachedData('latest_safety_results', safetyResults)

    // 更新道路数据的安全状态
    this.updateRoadSafetyStatus(safetyResults)

    // 如果启用了自动报告生成，触发报告生成
    if (this.integrationState.dataSync.safetyToReport) {
      this.triggerAutomaticReportGeneration(safetyResults)
    }
  }

  /**
   * 处理模块切换事件
   */
  private handleModuleSwitched(event: DataFlowEvent) {
    const { data } = event
    const { oldModule, newModule } = data

    console.log(`模块切换: ${oldModule} -> ${newModule}`)

    // 保存当前模块状态
    if (oldModule) {
      this.saveModuleState(oldModule)
    }

    // 恢复新模块状态
    if (newModule) {
      this.restoreModuleState(newModule)
    }

    // 同步相关数据到新模块
    this.syncDataToModule(newModule)
  }

  /**
   * 处理选择变化事件
   */
  private handleSelectionChanged(event: DataFlowEvent) {
    const { data } = event
    const { newSelection } = data

    console.log('处理选择变化事件', newSelection)

    // 同步选择状态到所有模块
    this.syncSelectionToAllModules(newSelection)

    // 如果选中了道路数据，预加载相关信息
    const selectedRoads = this.getSelectedRoadData(newSelection)
    if (selectedRoads.length > 0) {
      this.preloadRoadRelatedData(selectedRoads)
    }
  }

  /**
   * 同步GIS数据到道路模块
   */
  private syncGISDataToRoadModule() {
    const roadData = this.gisStore.allData.filter(data => data.type === 'road')
    const terrainData = this.gisStore.allData.filter(data => data.type === 'terrain')

    emitDataFlowEvent('data_updated', 'gis', {
      roadData,
      terrainData
    }, 'road', {
      syncType: 'gis_to_road'
    })
  }

  /**
   * 处理道路数据导入
   */
  private processRoadDataImport(roadData: GISData) {
    // 验证道路数据完整性
    if (this.validateRoadData(roadData)) {
      // 添加到GIS存储
      this.gisStore.addData(roadData)
      
      // 触发道路数据分析
      this.analyzeRoadData(roadData)
    } else {
      console.warn('道路数据验证失败', roadData)
    }
  }

  /**
   * 处理地形数据导入
   */
  private processTerrainDataImport(terrainData: GISData) {
    // 添加到GIS存储
    this.gisStore.addData(terrainData)
    
    // 更新地形相关的道路设计参数
    this.updateTerrainBasedParameters(terrainData)
  }

  /**
   * 处理钻孔数据导入
   */
  private processDrillingDataImport(drillingData: GISData) {
    // 添加到GIS存储
    this.gisStore.addData(drillingData)
    
    // 分析钻孔数据对道路设计的影响
    this.analyzeDrillingImpact(drillingData)
  }

  /**
   * 将道路设计结果添加到GIS
   */
  private addRoadDataToGIS(roadData: RoadData) {
    const gisData: GISData = {
      id: roadData.id,
      name: roadData.name,
      type: 'road',
      coordinates: roadData.centerline.points.map(p => [p.longitude, p.latitude, p.elevation]),
      metadata: {
        ...roadData.metadata,
        totalLength: roadData.centerline.totalLength,
        designDate: new Date().toISOString()
      },
      visible: true,
      createTime: new Date().toISOString()
    }

    this.gisStore.addData(gisData)
  }

  /**
   * 触发自动安全检测
   */
  private async triggerAutomaticSafetyCheck(roadData: RoadData) {
    try {
      const safetyChecker = new RoadSafetyChecker({
        roadType: roadData.metadata?.roadType || 'main',
        strictness: 'normal',
        checkItems: ['grade', 'radius', 'sight_distance', 'width']
      })

      const results = await safetyChecker.performSafetyCheck(roadData)
      
      emitDataFlowEvent('safety_checked', 'road', results, 'safety', {
        automatic: true,
        roadId: roadData.id
      })
    } catch (error) {
      console.error('自动安全检测失败:', error)
    }
  }

  /**
   * 更新道路安全状态
   */
  private updateRoadSafetyStatus(safetyResults: SafetyCheckResult[]) {
    const roadIds = new Set(safetyResults.map(r => r.location?.coordinates).filter(Boolean))
    
    roadIds.forEach(roadId => {
      const roadGISData = this.gisStore.allData.find(data => 
        data.type === 'road' && this.isRoadAtLocation(data, roadId as any)
      )
      
      if (roadGISData) {
        const roadResults = safetyResults.filter(r => 
          this.isRoadAtLocation(roadGISData, r.location?.coordinates as any)
        )
        
        const safetyScore = this.calculateSafetyScore(roadResults)
        
        // 更新道路的安全状态
        roadGISData.metadata = {
          ...roadGISData.metadata,
          safetyScore,
          safetyStatus: this.getSafetyStatus(safetyScore),
          lastSafetyCheck: new Date().toISOString()
        }
      }
    })
  }

  /**
   * 触发自动报告生成
   */
  private triggerAutomaticReportGeneration(safetyResults: SafetyCheckResult[]) {
    const reportData = {
      safetyResults,
      roadData: dataFlowManager.getCachedData('latest_road_design'),
      timestamp: new Date().toISOString()
    }

    emitDataFlowEvent('data_updated', 'safety', reportData, 'report', {
      automatic: true,
      reportType: 'safety_report'
    })
  }

  /**
   * 保存模块状态
   */
  private saveModuleState(module: string) {
    const state = dataFlowManager.getModuleState(module)
    if (state) {
      dataFlowManager.setCachedData(`${module}_state`, state)
    }
  }

  /**
   * 恢复模块状态
   */
  private restoreModuleState(module: string) {
    const savedState = dataFlowManager.getCachedData(`${module}_state`)
    if (savedState) {
      // 恢复模块的选择状态、视图状态等
      emitDataFlowEvent('data_updated', 'system', savedState, module, {
        restoreState: true
      })
    }
  }

  /**
   * 同步数据到指定模块
   */
  private syncDataToModule(module: string) {
    switch (module) {
      case 'road':
        this.syncGISDataToRoadModule()
        break
      case 'safety':
        this.syncRoadDataToSafetyModule()
        break
      case 'report':
        this.syncAllDataToReportModule()
        break
    }
  }

  /**
   * 同步道路数据到安全模块
   */
  private syncRoadDataToSafetyModule() {
    const roadData = this.gisStore.allData.filter(data => data.type === 'road')
    const latestDesign = dataFlowManager.getCachedData('latest_road_design')

    emitDataFlowEvent('data_updated', 'road', {
      roadData,
      latestDesign
    }, 'safety', {
      syncType: 'road_to_safety'
    })
  }

  /**
   * 同步所有数据到报告模块
   */
  private syncAllDataToReportModule() {
    const allData = {
      gisData: this.gisStore.allData,
      roadDesigns: dataFlowManager.getCachedData('road_designs') || [],
      safetyResults: dataFlowManager.getCachedData('safety_results') || [],
      eventHistory: dataFlowManager.getEventHistory(100)
    }

    emitDataFlowEvent('data_updated', 'system', allData, 'report', {
      syncType: 'all_to_report'
    })
  }

  /**
   * 同步选择到所有模块
   */
  private syncSelectionToAllModules(selection: string[]) {
    const modules = ['road', 'safety', 'data', 'report']
    
    modules.forEach(module => {
      emitDataFlowEvent('selection_changed', 'gis', { selection }, module)
    })
  }

  /**
   * 获取选中的道路数据
   */
  private getSelectedRoadData(selection: string[]): GISData[] {
    return this.gisStore.allData.filter(data => 
      data.type === 'road' && selection.includes(data.id)
    )
  }

  /**
   * 预加载道路相关数据
   */
  private preloadRoadRelatedData(roadData: GISData[]) {
    roadData.forEach(road => {
      // 预加载安全检测结果
      const safetyResults = dataFlowManager.getCachedData(`safety_${road.id}`)
      if (!safetyResults) {
        // 触发安全检测数据加载
        emitDataFlowEvent('data_updated', 'gis', { roadId: road.id }, 'safety', {
          preload: true
        })
      }
    })
  }

  // 辅助方法
  private validateRoadData(roadData: GISData): boolean {
    return !!(roadData.coordinates && roadData.coordinates.length > 0)
  }

  private analyzeRoadData(roadData: GISData) {
    // 分析道路数据的基本特征
    console.log('分析道路数据:', roadData.name)
  }

  private updateTerrainBasedParameters(terrainData: GISData) {
    // 根据地形数据更新道路设计参数
    console.log('更新地形相关参数:', terrainData.name)
  }

  private analyzeDrillingImpact(drillingData: GISData) {
    // 分析钻孔数据对道路设计的影响
    console.log('分析钻孔数据影响:', drillingData.name)
  }

  private isRoadAtLocation(roadData: GISData, location: number[]): boolean {
    // 简化的位置匹配逻辑
    return true
  }

  private calculateSafetyScore(results: SafetyCheckResult[]): number {
    if (results.length === 0) return 100
    
    const passCount = results.filter(r => r.status === 'pass').length
    const warningCount = results.filter(r => r.status === 'warning').length
    const failCount = results.filter(r => r.status === 'fail').length
    
    return Math.round((passCount * 100 + warningCount * 70) / results.length)
  }

  private getSafetyStatus(score: number): string {
    if (score >= 90) return 'excellent'
    if (score >= 80) return 'good'
    if (score >= 70) return 'acceptable'
    if (score >= 60) return 'warning'
    return 'critical'
  }

  /**
   * 获取集成状态
   */
  public getIntegrationState() {
    return this.integrationState
  }

  /**
   * 启用/禁用数据同步
   */
  public setDataSync(syncType: keyof typeof this.integrationState.dataSync, enabled: boolean) {
    this.integrationState.dataSync[syncType] = enabled
  }
}

// 导出单例实例
export const moduleIntegrationService = ModuleIntegrationService.getInstance()
