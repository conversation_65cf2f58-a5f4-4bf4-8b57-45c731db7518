@echo off
chcp 65001 >nul
title 露天矿山道路设计软件启动器

echo.
echo ========================================
echo    露天矿山道路设计软件启动器 v1.0
echo ========================================
echo.

:: 检查Node.js是否安装
echo [1/6] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到Node.js，请先安装Node.js
    echo    下载地址: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

:: 检查npm是否可用
echo [2/6] 检查npm包管理器...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: npm不可用
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: %NPM_VERSION%

:: 检查项目目录
echo [3/6] 检查项目文件...
if not exist "package.json" (
    echo ❌ 错误: 未找到package.json文件，请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "src" (
    echo ❌ 错误: 未找到src目录，项目文件不完整
    pause
    exit /b 1
)

echo ✅ 项目文件检查通过

:: 检查依赖是否安装
echo [4/6] 检查项目依赖...
if not exist "node_modules" (
    echo ⚠️  未检测到node_modules目录，正在安装依赖...
    echo    这可能需要几分钟时间，请耐心等待...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 错误: 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

:: 环境变量检查
echo [5/6] 检查环境配置...
if not exist ".env" (
    echo ⚠️  未找到.env文件，使用默认配置
) else (
    echo ✅ 环境配置文件存在
)

:: 启动开发服务器
echo [6/6] 启动开发服务器...
echo.
echo 🚀 正在启动露天矿山道路设计软件...
echo    - 服务器地址: http://localhost:3000
echo    - 按 Ctrl+C 可停止服务器
echo.

:: 设置环境变量
set NODE_ENV=development
set VITE_DEBUG_MODE=true

:: 启动服务器
npm run dev

:: 如果服务器异常退出
echo.
echo ⚠️  开发服务器已停止
pause
