/**
 * 数据流管理器单元测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { DataFlowManager, emitDataFlowEvent, onDataFlowEvent, offDataFlowEvent } from '@/utils/dataFlowManager'
import type { DataFlowEvent, DataFlowEventType } from '@/utils/dataFlowManager'

describe('DataFlowManager', () => {
  let dataFlowManager: DataFlowManager

  beforeEach(() => {
    dataFlowManager = DataFlowManager.getInstance()
    // 清理之前的状态
    dataFlowManager.clearCache()
  })

  afterEach(() => {
    // 清理测试后的状态
    dataFlowManager.clearCache()
  })

  describe('单例模式', () => {
    it('应该返回同一个实例', () => {
      const instance1 = DataFlowManager.getInstance()
      const instance2 = DataFlowManager.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })

  describe('事件监听和触发', () => {
    it('应该能够注册和触发事件监听器', () => {
      const mockCallback = vi.fn()
      const eventType: DataFlowEventType = 'data_updated'
      
      dataFlowManager.on(eventType, mockCallback)
      
      const testEvent: DataFlowEvent = {
        type: eventType,
        source: 'test',
        data: { test: 'data' },
        timestamp: Date.now()
      }
      
      dataFlowManager.emit(testEvent)
      
      expect(mockCallback).toHaveBeenCalledWith(testEvent)
    })

    it('应该能够移除事件监听器', () => {
      const mockCallback = vi.fn()
      const eventType: DataFlowEventType = 'data_updated'
      
      dataFlowManager.on(eventType, mockCallback)
      dataFlowManager.off(eventType, mockCallback)
      
      const testEvent: DataFlowEvent = {
        type: eventType,
        source: 'test',
        data: { test: 'data' },
        timestamp: Date.now()
      }
      
      dataFlowManager.emit(testEvent)
      
      expect(mockCallback).not.toHaveBeenCalled()
    })

    it('应该支持多个监听器', () => {
      const mockCallback1 = vi.fn()
      const mockCallback2 = vi.fn()
      const eventType: DataFlowEventType = 'data_updated'
      
      dataFlowManager.on(eventType, mockCallback1)
      dataFlowManager.on(eventType, mockCallback2)
      
      const testEvent: DataFlowEvent = {
        type: eventType,
        source: 'test',
        data: { test: 'data' },
        timestamp: Date.now()
      }
      
      dataFlowManager.emit(testEvent)
      
      expect(mockCallback1).toHaveBeenCalledWith(testEvent)
      expect(mockCallback2).toHaveBeenCalledWith(testEvent)
    })

    it('应该处理监听器中的错误', () => {
      const errorCallback = vi.fn(() => {
        throw new Error('Test error')
      })
      const normalCallback = vi.fn()
      const eventType: DataFlowEventType = 'data_updated'
      
      dataFlowManager.on(eventType, errorCallback)
      dataFlowManager.on(eventType, normalCallback)
      
      const testEvent: DataFlowEvent = {
        type: eventType,
        source: 'test',
        data: { test: 'data' },
        timestamp: Date.now()
      }
      
      // 应该不会抛出错误，并且正常的回调仍然会被调用
      expect(() => dataFlowManager.emit(testEvent)).not.toThrow()
      expect(normalCallback).toHaveBeenCalledWith(testEvent)
    })
  })

  describe('事件历史记录', () => {
    it('应该记录事件历史', () => {
      const testEvent: DataFlowEvent = {
        type: 'data_updated',
        source: 'test',
        data: { test: 'data' },
        timestamp: Date.now()
      }
      
      dataFlowManager.emit(testEvent)
      
      const history = dataFlowManager.getEventHistory()
      expect(history).toContain(testEvent)
    })

    it('应该限制历史记录数量', () => {
      // 发送大量事件
      for (let i = 0; i < 1100; i++) {
        const testEvent: DataFlowEvent = {
          type: 'data_updated',
          source: 'test',
          data: { index: i },
          timestamp: Date.now()
        }
        dataFlowManager.emit(testEvent)
      }
      
      const history = dataFlowManager.getEventHistory()
      expect(history.length).toBeLessThanOrEqual(1000)
    })

    it('应该支持限制历史记录查询数量', () => {
      // 发送多个事件
      for (let i = 0; i < 10; i++) {
        const testEvent: DataFlowEvent = {
          type: 'data_updated',
          source: 'test',
          data: { index: i },
          timestamp: Date.now()
        }
        dataFlowManager.emit(testEvent)
      }
      
      const limitedHistory = dataFlowManager.getEventHistory(5)
      expect(limitedHistory.length).toBe(5)
    })
  })

  describe('模块状态管理', () => {
    it('应该能够获取模块状态', () => {
      const moduleState = dataFlowManager.getModuleState('gis')
      
      expect(moduleState).toBeDefined()
      expect(moduleState?.module).toBe('gis')
      expect(moduleState?.active).toBeDefined()
      expect(moduleState?.selectedData).toBeDefined()
      expect(moduleState?.viewState).toBeDefined()
      expect(moduleState?.lastUpdate).toBeDefined()
    })

    it('应该在事件触发时更新模块状态', () => {
      const testEvent: DataFlowEvent = {
        type: 'data_updated',
        source: 'gis',
        target: 'road',
        data: { test: 'data' },
        timestamp: Date.now(),
        metadata: { viewState: { zoom: 10 } }
      }
      
      const beforeState = dataFlowManager.getModuleState('road')
      const beforeUpdate = beforeState?.lastUpdate || 0
      
      dataFlowManager.emit(testEvent)
      
      const afterState = dataFlowManager.getModuleState('road')
      expect(afterState?.lastUpdate).toBeGreaterThan(beforeUpdate)
      expect(afterState?.viewState.zoom).toBe(10)
    })
  })

  describe('数据缓存', () => {
    it('应该能够设置和获取缓存数据', () => {
      const testData = { test: 'cache data' }
      const key = 'test_cache'
      
      dataFlowManager.setCachedData(key, testData)
      const cachedData = dataFlowManager.getCachedData(key)
      
      expect(cachedData).toEqual(testData)
    })

    it('应该能够清理缓存', () => {
      const testData = { test: 'cache data' }
      const key = 'test_cache'
      
      dataFlowManager.setCachedData(key, testData)
      dataFlowManager.clearCache()
      
      const cachedData = dataFlowManager.getCachedData(key)
      expect(cachedData).toBeUndefined()
    })
  })
})

describe('便捷函数', () => {
  let mockCallback: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockCallback = vi.fn()
  })

  describe('emitDataFlowEvent', () => {
    it('应该能够触发数据流事件', () => {
      onDataFlowEvent('data_updated', mockCallback)
      
      emitDataFlowEvent('data_updated', 'test', { test: 'data' })
      
      expect(mockCallback).toHaveBeenCalled()
      const calledEvent = mockCallback.mock.calls[0][0]
      expect(calledEvent.type).toBe('data_updated')
      expect(calledEvent.source).toBe('test')
      expect(calledEvent.data).toEqual({ test: 'data' })
    })

    it('应该支持目标和元数据参数', () => {
      onDataFlowEvent('module_switched', mockCallback)
      
      emitDataFlowEvent(
        'module_switched',
        'gis',
        { oldModule: 'gis', newModule: 'road' },
        'road',
        { automatic: true }
      )
      
      expect(mockCallback).toHaveBeenCalled()
      const calledEvent = mockCallback.mock.calls[0][0]
      expect(calledEvent.target).toBe('road')
      expect(calledEvent.metadata?.automatic).toBe(true)
    })
  })

  describe('onDataFlowEvent', () => {
    it('应该能够注册事件监听器', () => {
      onDataFlowEvent('safety_checked', mockCallback)
      
      emitDataFlowEvent('safety_checked', 'safety', { results: [] })
      
      expect(mockCallback).toHaveBeenCalled()
    })
  })

  describe('offDataFlowEvent', () => {
    it('应该能够移除事件监听器', () => {
      onDataFlowEvent('road_designed', mockCallback)
      offDataFlowEvent('road_designed', mockCallback)
      
      emitDataFlowEvent('road_designed', 'road', { roadData: {} })
      
      expect(mockCallback).not.toHaveBeenCalled()
    })
  })
})

describe('集成测试', () => {
  it('应该支持复杂的事件流', () => {
    const dataImportCallback = vi.fn()
    const dataUpdateCallback = vi.fn()
    const moduleSwitchCallback = vi.fn()
    
    onDataFlowEvent('data_imported', dataImportCallback)
    onDataFlowEvent('data_updated', dataUpdateCallback)
    onDataFlowEvent('module_switched', moduleSwitchCallback)
    
    // 模拟数据导入流程
    emitDataFlowEvent('data_imported', 'data', { 
      type: 'road', 
      name: 'test road' 
    })
    
    // 模拟数据更新流程
    emitDataFlowEvent('data_updated', 'gis', { 
      roadData: { id: '1', name: 'test road' } 
    }, 'road')
    
    // 模拟模块切换流程
    emitDataFlowEvent('module_switched', 'gis', { 
      oldModule: 'gis', 
      newModule: 'road' 
    }, 'road')
    
    expect(dataImportCallback).toHaveBeenCalledTimes(1)
    expect(dataUpdateCallback).toHaveBeenCalledTimes(1)
    expect(moduleSwitchCallback).toHaveBeenCalledTimes(1)
    
    // 检查事件历史
    const history = DataFlowManager.getInstance().getEventHistory()
    expect(history.length).toBe(3)
    expect(history.map(e => e.type)).toContain('data_imported')
    expect(history.map(e => e.type)).toContain('data_updated')
    expect(history.map(e => e.type)).toContain('module_switched')
  })

  it('应该正确处理事件链', async () => {
    const events: string[] = []
    
    onDataFlowEvent('data_imported', () => {
      events.push('data_imported')
      // 数据导入后触发数据更新
      emitDataFlowEvent('data_updated', 'data', { updated: true })
    })
    
    onDataFlowEvent('data_updated', () => {
      events.push('data_updated')
      // 数据更新后触发模块切换
      emitDataFlowEvent('module_switched', 'system', { switched: true })
    })
    
    onDataFlowEvent('module_switched', () => {
      events.push('module_switched')
    })
    
    // 触发事件链
    emitDataFlowEvent('data_imported', 'user', { file: 'test.json' })
    
    // 等待事件链完成
    await new Promise(resolve => setTimeout(resolve, 0))
    
    expect(events).toEqual(['data_imported', 'data_updated', 'module_switched'])
  })
})

describe('性能测试', () => {
  it('应该高效处理大量事件', () => {
    const startTime = performance.now()
    
    // 发送大量事件
    for (let i = 0; i < 1000; i++) {
      emitDataFlowEvent('data_updated', 'test', { index: i })
    }
    
    const endTime = performance.now()
    const executionTime = endTime - startTime
    
    expect(executionTime).toBeLessThan(100) // 应该在100ms内完成
  })

  it('应该高效处理大量监听器', () => {
    const callbacks = []
    
    // 注册大量监听器
    for (let i = 0; i < 100; i++) {
      const callback = vi.fn()
      callbacks.push(callback)
      onDataFlowEvent('data_updated', callback)
    }
    
    const startTime = performance.now()
    
    // 触发事件
    emitDataFlowEvent('data_updated', 'test', { test: 'data' })
    
    const endTime = performance.now()
    const executionTime = endTime - startTime
    
    expect(executionTime).toBeLessThan(50) // 应该在50ms内完成
    
    // 验证所有监听器都被调用
    callbacks.forEach(callback => {
      expect(callback).toHaveBeenCalledTimes(1)
    })
  })
})
