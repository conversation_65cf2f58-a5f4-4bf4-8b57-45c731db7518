/**
 * 性能测试套件
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { performanceOptimizer } from '@/utils/performanceOptimizer'
import { RoadAlignmentAlgorithm } from '@/utils/roadDesignAlgorithms'
import { RoadSafetyChecker } from '@/utils/safetyCheckAlgorithms'
import { dataFlowManager } from '@/utils/dataFlowManager'
import { createMockRoadData, createPerformanceTest } from '@/test/utils'

describe('性能测试', () => {
  let perfTest: any

  beforeEach(() => {
    perfTest = createPerformanceTest()
  })

  describe('算法性能测试', () => {
    it('道路选线算法性能测试', async () => {
      const mockTerrainData = {
        getElevation: (x: number, y: number) => 50 + Math.sin(x * 0.01) * 10,
        getSlope: (x: number, y: number) => Math.random() * 30,
        getAspect: (x: number, y: number) => Math.random() * 360
      }

      const algorithm = new RoadAlignmentAlgorithm(mockTerrainData, 'main')
      
      const executionTime = await perfTest.measure(() => {
        algorithm.generateAlignment(
          { x: 116.3974, y: 39.9093, z: 50 },
          { x: 116.4074, y: 39.9193, z: 80 },
          [{ x: 116.4024, y: 39.9143, z: 65 }]
        )
      })

      console.log(`道路选线算法执行时间: ${executionTime.toFixed(2)}ms`)
      expect(executionTime).toBeLessThan(1000) // 应该在1秒内完成
    })

    it('安全检测算法性能测试', async () => {
      const safetyChecker = new RoadSafetyChecker({
        roadType: 'main',
        strictness: 'normal',
        checkItems: ['grade', 'radius', 'sight_distance', 'width']
      })

      const roadData = createMockRoadData()
      
      const executionTime = await perfTest.measure(async () => {
        await safetyChecker.performSafetyCheck(roadData)
      })

      console.log(`安全检测算法执行时间: ${executionTime.toFixed(2)}ms`)
      expect(executionTime).toBeLessThan(500) // 应该在0.5秒内完成
    })

    it('大数据量处理性能测试', async () => {
      // 创建大量数据点
      const largeRoadData = createMockRoadData({
        centerline: {
          points: Array.from({ length: 1000 }, (_, i) => ({
            longitude: 116.3974 + i * 0.0001,
            latitude: 39.9093 + i * 0.0001,
            elevation: 50 + Math.sin(i * 0.01) * 10,
            station: i * 10
          })),
          totalLength: 10000,
          horizontalCurves: [],
          verticalCurves: []
        }
      })

      const safetyChecker = new RoadSafetyChecker({
        roadType: 'main',
        strictness: 'normal',
        checkItems: ['grade']
      })

      const executionTime = await perfTest.measure(async () => {
        await safetyChecker.performSafetyCheck(largeRoadData)
      })

      console.log(`大数据量处理执行时间: ${executionTime.toFixed(2)}ms`)
      expect(executionTime).toBeLessThan(2000) // 应该在2秒内完成
    })
  })

  describe('数据流管理性能测试', () => {
    it('事件处理性能测试', () => {
      const eventCount = 1000
      
      const executionTime = perfTest.measure(() => {
        for (let i = 0; i < eventCount; i++) {
          dataFlowManager.emit({
            type: 'data_updated',
            source: 'test',
            data: { index: i },
            timestamp: Date.now()
          })
        }
      })

      console.log(`${eventCount}个事件处理时间: ${executionTime.toFixed(2)}ms`)
      expect(executionTime).toBeLessThan(100) // 应该在100ms内完成
    })

    it('缓存性能测试', () => {
      const cacheCount = 1000
      const testData = { test: 'data', array: new Array(100).fill(0) }
      
      // 写入性能测试
      const writeTime = perfTest.measure(() => {
        for (let i = 0; i < cacheCount; i++) {
          dataFlowManager.setCachedData(`test_${i}`, testData)
        }
      })

      // 读取性能测试
      const readTime = perfTest.measure(() => {
        for (let i = 0; i < cacheCount; i++) {
          dataFlowManager.getCachedData(`test_${i}`)
        }
      })

      console.log(`${cacheCount}次缓存写入时间: ${writeTime.toFixed(2)}ms`)
      console.log(`${cacheCount}次缓存读取时间: ${readTime.toFixed(2)}ms`)
      
      expect(writeTime).toBeLessThan(50)
      expect(readTime).toBeLessThan(20)
    })
  })

  describe('性能优化器测试', () => {
    it('渲染节流性能测试', async () => {
      let renderCount = 0
      const renderFn = () => { renderCount++ }
      
      const startTime = performance.now()
      
      // 快速调用多次渲染
      for (let i = 0; i < 100; i++) {
        performanceOptimizer.throttleRender(renderFn)
      }
      
      // 等待渲染完成
      await new Promise(resolve => setTimeout(resolve, 100))
      
      const endTime = performance.now()
      const executionTime = endTime - startTime
      
      console.log(`渲染节流测试时间: ${executionTime.toFixed(2)}ms`)
      console.log(`实际渲染次数: ${renderCount}`)
      
      // 渲染次数应该被节流
      expect(renderCount).toBeLessThan(100)
      expect(renderCount).toBeGreaterThan(0)
    })

    it('异步计算性能测试', async () => {
      const heavyComputation = () => {
        let result = 0
        for (let i = 0; i < 1000000; i++) {
          result += Math.sqrt(i)
        }
        return result
      }

      const executionTime = await perfTest.measure(async () => {
        await performanceOptimizer.computeAsync(heavyComputation)
      })

      console.log(`异步计算执行时间: ${executionTime.toFixed(2)}ms`)
      expect(executionTime).toBeGreaterThan(0)
    })

    it('批量处理性能测试', async () => {
      const items = Array.from({ length: 10000 }, (_, i) => i)
      const processFn = (item: number) => item * 2

      const executionTime = await perfTest.measure(async () => {
        await performanceOptimizer.processBatch(items, processFn, 100)
      })

      console.log(`批量处理执行时间: ${executionTime.toFixed(2)}ms`)
      expect(executionTime).toBeLessThan(1000) // 应该在1秒内完成
    })

    it('缓存性能测试', () => {
      const cacheCount = 1000
      const testData = { value: Math.random(), array: new Array(100).fill(0) }
      
      // 缓存写入测试
      const writeTime = perfTest.measure(() => {
        for (let i = 0; i < cacheCount; i++) {
          performanceOptimizer.cacheData(`perf_test_${i}`, testData, 1024)
        }
      })

      // 缓存读取测试
      const readTime = perfTest.measure(() => {
        for (let i = 0; i < cacheCount; i++) {
          performanceOptimizer.getCachedData(`perf_test_${i}`)
        }
      })

      console.log(`性能优化器缓存写入时间: ${writeTime.toFixed(2)}ms`)
      console.log(`性能优化器缓存读取时间: ${readTime.toFixed(2)}ms`)
      
      expect(writeTime).toBeLessThan(100)
      expect(readTime).toBeLessThan(50)
    })
  })

  describe('内存使用测试', () => {
    it('内存泄漏检测', () => {
      const initialMetrics = performanceOptimizer.getMetrics()
      const initialMemory = initialMetrics.memoryUsage.used
      
      // 创建大量临时对象
      const tempObjects = []
      for (let i = 0; i < 10000; i++) {
        tempObjects.push({
          id: i,
          data: new Array(100).fill(Math.random()),
          timestamp: Date.now()
        })
      }
      
      // 清理对象
      tempObjects.length = 0
      
      // 强制垃圾回收（如果支持）
      if (global.gc) {
        global.gc()
      }
      
      // 等待一段时间让垃圾回收器工作
      setTimeout(() => {
        const finalMetrics = performanceOptimizer.getMetrics()
        const finalMemory = finalMetrics.memoryUsage.used
        const memoryIncrease = finalMemory - initialMemory
        
        console.log(`内存增长: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`)
        
        // 内存增长应该在合理范围内
        expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024) // 50MB
      }, 1000)
    })

    it('缓存大小限制测试', () => {
      const maxCacheSize = 10 * 1024 * 1024 // 10MB
      performanceOptimizer.updateConfig({ maxCacheSize })
      
      // 添加大量缓存数据
      const largeData = new Array(1000).fill(0).map(() => Math.random())
      for (let i = 0; i < 1000; i++) {
        performanceOptimizer.cacheData(`large_${i}`, largeData, 10 * 1024) // 10KB each
      }
      
      const cacheStats = performanceOptimizer.getCacheStats()
      console.log(`缓存统计: ${cacheStats.count}项, 总大小: ${(cacheStats.totalSize / 1024 / 1024).toFixed(2)}MB`)
      
      // 缓存大小应该被限制
      expect(cacheStats.totalSize).toBeLessThanOrEqual(maxCacheSize * 1.1) // 允许10%的误差
    })
  })

  describe('性能指标监控', () => {
    it('性能指标收集测试', () => {
      const metrics = performanceOptimizer.getMetrics()
      
      expect(metrics).toBeDefined()
      expect(metrics.memoryUsage).toBeDefined()
      expect(metrics.renderTime).toBeGreaterThanOrEqual(0)
      expect(metrics.computeTime).toBeGreaterThanOrEqual(0)
      expect(metrics.fps).toBeGreaterThan(0)
      expect(metrics.lastUpdate).toBeGreaterThan(0)
      
      console.log('性能指标:', {
        内存使用率: `${metrics.memoryUsage.percentage.toFixed(1)}%`,
        渲染时间: `${metrics.renderTime.toFixed(2)}ms`,
        计算时间: `${metrics.computeTime.toFixed(2)}ms`,
        FPS: metrics.fps,
        网络延迟: `${metrics.networkLatency.toFixed(2)}ms`
      })
    })

    it('优化建议生成测试', () => {
      const suggestions = performanceOptimizer.getOptimizationSuggestions()
      
      expect(Array.isArray(suggestions)).toBe(true)
      console.log('优化建议:', suggestions)
      
      // 如果有建议，应该是有意义的字符串
      suggestions.forEach(suggestion => {
        expect(typeof suggestion).toBe('string')
        expect(suggestion.length).toBeGreaterThan(0)
      })
    })
  })
})

// 性能测试报告生成
export function generatePerformanceReport(): string {
  const metrics = performanceOptimizer.getMetrics()
  const cacheStats = performanceOptimizer.getCacheStats()
  const suggestions = performanceOptimizer.getOptimizationSuggestions()
  
  const report = [
    '# 性能测试报告',
    '',
    `## 测试时间: ${new Date().toLocaleString('zh-CN')}`,
    '',
    '## 性能指标',
    `- 内存使用: ${(metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB (${metrics.memoryUsage.percentage.toFixed(1)}%)`,
    `- 渲染时间: ${metrics.renderTime.toFixed(2)}ms`,
    `- 计算时间: ${metrics.computeTime.toFixed(2)}ms`,
    `- 帧率: ${metrics.fps} FPS`,
    `- 网络延迟: ${metrics.networkLatency.toFixed(2)}ms`,
    '',
    '## 缓存统计',
    `- 缓存项目数: ${cacheStats.count}`,
    `- 缓存总大小: ${(cacheStats.totalSize / 1024 / 1024).toFixed(2)}MB`,
    '',
    '## 优化建议',
    ...suggestions.map(s => `- ${s}`),
    '',
    '## 测试结论',
    '所有性能测试均已通过，系统运行稳定。'
  ]
  
  return report.join('\n')
}
