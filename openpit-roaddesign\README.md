# 露天矿山道路设计软件

基于Vue 3 + TypeScript + Cesium的专业露天矿山道路设计系统

## 🌟 项目特色

- **专业性**: 严格按照露天矿山道路设计标准开发
- **3D可视化**: 基于Cesium引擎的专业级3D地理信息系统
- **智能化**: 自动化道路设计、智能安全检测、优化建议
- **模块化**: 完整的数据管理、设计分析、安全检测工作流程
- **高性能**: 内置性能优化、内存管理、响应式设计
- **高质量**: 完整测试覆盖、错误处理、日志系统

## 🚀 快速开始

### 系统要求

- **Node.js**: 16.0+ (推荐18.0+)
- **npm**: 8.0+ 或 yarn 1.22+
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **内存**: 4GB+ (推荐8GB+)
- **显卡**: 支持WebGL 2.0

### 一键启动

#### Windows用户
```bash
# 双击运行启动脚本
start.bat

# 或者调试模式启动
debug.bat
```

#### Linux/Mac用户
```bash
# 添加执行权限
chmod +x start.sh

# 运行启动脚本
./start.sh
```

### 手动启动

```bash
# 1. 克隆项目
git clone <repository-url>
cd openpit-roaddesign

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 打开浏览器访问
# http://localhost:3000
```

## 🛠️ 开发工具

### 可用脚本

```bash
# 开发
npm run dev          # 启动开发服务器

# 构建
npm run build        # 生产构建
npm run preview      # 预览构建结果

# 测试
npm run test         # 运行测试
npm run test:unit    # 单元测试
npm run test:watch   # 监听模式测试
npm run test:coverage # 测试覆盖率
npm run test:ui      # 测试UI界面
npm run test:e2e     # 端到端测试

# 代码质量
npm run lint         # 代码检查
npm run format       # 代码格式化
npm run type-check   # 类型检查
```

## 🏗️ 核心功能

### 1. GIS数据管理
- 3D地球视图 (Cesium)
- 多格式数据导入 (GeoJSON, KML, Shapefile)
- 图层管理和可视化
- 测量工具 (距离、面积、高程)

### 2. 数据管理系统
- 地形数据管理
- 钻孔数据管理
- 地质数据管理
- CAD数据集成
- 批量操作支持

### 3. 道路设计
- 自动道路选线 (A*算法)
- 路线优化 (遗传算法)
- 纵横断面生成
- 冲突检测
- 设计标准集成

### 4. 安全检测
- 坡度检测
- 转弯半径检测
- 视距检测
- 道路宽度检测
- 安全评分系统
- 改进建议生成

### 5. 报告输出
- 自动报告生成
- 多格式导出 (PDF, Word, HTML)
- 图表可视化
- 数据统计分析

### 6. 系统监控
- 性能监控
- 内存使用监控
- 数据流监控
- 错误日志记录
- 健康状态检查

## 🔧 配置说明

### 环境变量

创建 `.env.local` 文件进行本地配置：

```env
# Cesium配置
VITE_CESIUM_TOKEN=your-cesium-ion-token

# API配置
VITE_API_BASE_URL=http://localhost:8080/api

# 调试配置
VITE_DEBUG_MODE=true
VITE_LOG_LEVEL=debug
VITE_SHOW_PERFORMANCE_MONITOR=true
```

## 🐛 故障排除

### 常见问题

1. **Cesium加载失败**
   - 检查网络连接
   - 验证Cesium Token
   - 确保HTTPS环境

2. **内存不足**
   - 关闭不必要的浏览器标签
   - 清理浏览器缓存
   - 增加系统内存

3. **性能问题**
   - 启用性能监控
   - 检查GPU加速
   - 优化数据量

### 系统诊断

运行诊断脚本获取详细系统信息：

```bash
# Windows
diagnose.bat

# Linux/Mac
./diagnose.sh
```

## 📞 支持

- **问题反馈**: GitHub Issues
- **技术支持**: 查看诊断报告
- **文档**: 项目README和代码注释

---

**露天矿山道路设计软件** - 专业、智能、高效的道路设计解决方案
