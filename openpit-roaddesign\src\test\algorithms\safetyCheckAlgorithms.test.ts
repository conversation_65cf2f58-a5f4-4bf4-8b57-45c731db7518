/**
 * 安全检测算法单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { RoadSafetyChecker, SafetyReportGenerator } from '@/utils/safetyCheckAlgorithms'
import { createMockRoadData, createMockSafetyResults } from '@/test/utils'
import type { SafetyCheckConfig } from '@/utils/safetyCheckAlgorithms'

describe('RoadSafetyChecker', () => {
  let safetyChecker: RoadSafetyChecker
  let config: SafetyCheckConfig

  beforeEach(() => {
    config = {
      roadType: 'main',
      strictness: 'normal',
      checkItems: ['grade', 'radius', 'sight_distance', 'width']
    }
    safetyChecker = new RoadSafetyChecker(config)
  })

  describe('构造函数', () => {
    it('应该正确初始化安全检测器', () => {
      expect(safetyChecker).toBeDefined()
    })

    it('应该支持不同的道路类型', () => {
      const mainRoadChecker = new RoadSafetyChecker({ ...config, roadType: 'main' })
      const branchRoadChecker = new RoadSafetyChecker({ ...config, roadType: 'branch' })
      
      expect(mainRoadChecker).toBeDefined()
      expect(branchRoadChecker).toBeDefined()
    })

    it('应该支持不同的严格程度', () => {
      const strictChecker = new RoadSafetyChecker({ ...config, strictness: 'strict' })
      const looseChecker = new RoadSafetyChecker({ ...config, strictness: 'loose' })
      
      expect(strictChecker).toBeDefined()
      expect(looseChecker).toBeDefined()
    })
  })

  describe('performSafetyCheck', () => {
    it('应该执行完整的安全检测', async () => {
      const roadData = createMockRoadData()
      
      const results = await safetyChecker.performSafetyCheck(roadData)
      
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
    })

    it('应该根据配置执行指定的检测项目', async () => {
      const roadData = createMockRoadData()
      const specificConfig = {
        ...config,
        checkItems: ['grade', 'radius']
      }
      const specificChecker = new RoadSafetyChecker(specificConfig)
      
      const results = await specificChecker.performSafetyCheck(roadData)
      
      expect(results).toBeDefined()
      // 结果应该只包含指定的检测项目类型
      const resultTypes = new Set(results.map(r => r.type))
      resultTypes.forEach(type => {
        expect(['grade', 'radius']).toContain(type)
      })
    })

    it('应该处理空的道路数据', async () => {
      const emptyRoadData = createMockRoadData({
        centerline: {
          points: [],
          totalLength: 0,
          horizontalCurves: [],
          verticalCurves: []
        }
      })
      
      const results = await safetyChecker.performSafetyCheck(emptyRoadData)
      
      expect(results).toBeDefined()
      expect(Array.isArray(results)).toBe(true)
    })
  })

  describe('坡度检测', () => {
    it('应该检测超标的纵坡', async () => {
      const roadData = createMockRoadData({
        centerline: {
          points: [
            { longitude: 116.3974, latitude: 39.9093, elevation: 50, station: 0 },
            { longitude: 116.3984, latitude: 39.9103, elevation: 70, station: 100 } // 20%坡度，超标
          ],
          totalLength: 100,
          horizontalCurves: [],
          verticalCurves: []
        }
      })
      
      const gradeChecker = new RoadSafetyChecker({
        ...config,
        checkItems: ['grade']
      })
      
      const results = await gradeChecker.performSafetyCheck(roadData)
      
      const gradeResults = results.filter(r => r.type === 'grade')
      expect(gradeResults.length).toBeGreaterThan(0)
      expect(gradeResults[0].status).toBe('fail')
    })

    it('应该识别合格的坡度', async () => {
      const roadData = createMockRoadData({
        centerline: {
          points: [
            { longitude: 116.3974, latitude: 39.9093, elevation: 50, station: 0 },
            { longitude: 116.3984, latitude: 39.9103, elevation: 55, station: 100 } // 5%坡度，合格
          ],
          totalLength: 100,
          horizontalCurves: [],
          verticalCurves: []
        }
      })
      
      const gradeChecker = new RoadSafetyChecker({
        ...config,
        checkItems: ['grade']
      })
      
      const results = await gradeChecker.performSafetyCheck(roadData)
      
      // 如果有坡度检测结果，应该是通过的
      const gradeResults = results.filter(r => r.type === 'grade')
      if (gradeResults.length > 0) {
        expect(gradeResults[0].status).toBe('pass')
      }
    })
  })

  describe('转弯半径检测', () => {
    it('应该检测过小的转弯半径', async () => {
      const roadData = createMockRoadData({
        centerline: {
          points: [
            { longitude: 116.3974, latitude: 39.9093, elevation: 50, station: 0 },
            { longitude: 116.3984, latitude: 39.9103, elevation: 55, station: 100 }
          ],
          totalLength: 100,
          horizontalCurves: [
            { station: 50, radius: 30, length: 50, direction: 'left' } // 半径过小
          ],
          verticalCurves: []
        }
      })
      
      const radiusChecker = new RoadSafetyChecker({
        ...config,
        checkItems: ['radius']
      })
      
      const results = await radiusChecker.performSafetyCheck(roadData)
      
      const radiusResults = results.filter(r => r.type === 'radius')
      expect(radiusResults.length).toBeGreaterThan(0)
      expect(['warning', 'fail']).toContain(radiusResults[0].status)
    })

    it('应该识别合格的转弯半径', async () => {
      const roadData = createMockRoadData({
        centerline: {
          points: [
            { longitude: 116.3974, latitude: 39.9093, elevation: 50, station: 0 },
            { longitude: 116.3984, latitude: 39.9103, elevation: 55, station: 100 }
          ],
          totalLength: 100,
          horizontalCurves: [
            { station: 50, radius: 80, length: 50, direction: 'left' } // 半径合格
          ],
          verticalCurves: []
        }
      })
      
      const radiusChecker = new RoadSafetyChecker({
        ...config,
        checkItems: ['radius']
      })
      
      const results = await radiusChecker.performSafetyCheck(roadData)
      
      const radiusResults = results.filter(r => r.type === 'radius')
      if (radiusResults.length > 0) {
        expect(radiusResults[0].status).toBe('pass')
      }
    })
  })

  describe('视距检测', () => {
    it('应该检测视距不足的情况', async () => {
      const roadData = createMockRoadData()
      
      const sightChecker = new RoadSafetyChecker({
        ...config,
        checkItems: ['sight_distance']
      })
      
      const results = await sightChecker.performSafetyCheck(roadData)
      
      expect(results).toBeDefined()
      const sightResults = results.filter(r => r.type === 'sight_distance')
      // 应该有视距检测结果
      expect(sightResults.length).toBeGreaterThanOrEqual(0)
    })
  })

  describe('道路宽度检测', () => {
    it('应该检测宽度不足的道路', async () => {
      const roadData = createMockRoadData({
        metadata: {
          width: 4, // 宽度不足
          roadType: 'main',
          designSpeed: 40
        }
      })
      
      const widthChecker = new RoadSafetyChecker({
        ...config,
        checkItems: ['width']
      })
      
      const results = await widthChecker.performSafetyCheck(roadData)
      
      const widthResults = results.filter(r => r.type === 'width')
      expect(widthResults.length).toBeGreaterThan(0)
      expect(['warning', 'fail']).toContain(widthResults[0].status)
    })

    it('应该识别宽度合格的道路', async () => {
      const roadData = createMockRoadData({
        metadata: {
          width: 8, // 宽度合格
          roadType: 'main',
          designSpeed: 40
        }
      })
      
      const widthChecker = new RoadSafetyChecker({
        ...config,
        checkItems: ['width']
      })
      
      const results = await widthChecker.performSafetyCheck(roadData)
      
      const widthResults = results.filter(r => r.type === 'width')
      if (widthResults.length > 0) {
        expect(widthResults[0].status).toBe('pass')
      }
    })
  })

  describe('generateSummary', () => {
    it('应该生成正确的安全检测摘要', () => {
      const results = createMockSafetyResults(5)
      
      const summary = safetyChecker.generateSummary(results)
      
      expect(summary).toBeDefined()
      expect(summary.totalChecks).toBe(5)
      expect(summary.passCount).toBeGreaterThanOrEqual(0)
      expect(summary.warningCount).toBeGreaterThanOrEqual(0)
      expect(summary.failCount).toBeGreaterThanOrEqual(0)
      expect(summary.safetyScore).toBeGreaterThanOrEqual(0)
      expect(summary.safetyScore).toBeLessThanOrEqual(100)
      expect(Array.isArray(summary.criticalIssues)).toBe(true)
    })

    it('应该正确计算安全评分', () => {
      const allPassResults = [
        { type: 'grade', status: 'pass', value: 5, standard: 8, message: 'pass', location: null },
        { type: 'radius', status: 'pass', value: 60, standard: 50, message: 'pass', location: null }
      ] as any[]
      
      const summary = safetyChecker.generateSummary(allPassResults)
      expect(summary.safetyScore).toBe(100)
      
      const allFailResults = [
        { type: 'grade', status: 'fail', value: 12, standard: 8, message: 'fail', location: null },
        { type: 'radius', status: 'fail', value: 30, standard: 50, message: 'fail', location: null }
      ] as any[]
      
      const failSummary = safetyChecker.generateSummary(allFailResults)
      expect(failSummary.safetyScore).toBe(0)
    })

    it('应该识别关键问题', () => {
      const results = [
        { type: 'grade', status: 'fail', value: 15, standard: 8, message: 'critical', location: null },
        { type: 'radius', status: 'warning', value: 45, standard: 50, message: 'warning', location: null },
        { type: 'width', status: 'pass', value: 7, standard: 6, message: 'pass', location: null }
      ] as any[]
      
      const summary = safetyChecker.generateSummary(results)
      
      expect(summary.criticalIssues.length).toBeGreaterThan(0)
      expect(summary.criticalIssues[0].status).toBe('fail')
    })
  })
})

describe('SafetyReportGenerator', () => {
  describe('generateReport', () => {
    it('应该生成完整的安全检测报告', () => {
      const roadData = createMockRoadData()
      const results = createMockSafetyResults(3)
      const summary = {
        totalChecks: 3,
        passCount: 1,
        warningCount: 1,
        failCount: 1,
        safetyScore: 70,
        criticalIssues: results.filter(r => r.status === 'fail')
      }
      
      const report = SafetyReportGenerator.generateReport(roadData, results, summary)
      
      expect(report).toBeDefined()
      expect(typeof report).toBe('string')
      expect(report).toContain('# 道路安全检测报告')
      expect(report).toContain('## 基本信息')
      expect(report).toContain('## 检测结果摘要')
      expect(report).toContain('## 详细检测结果')
      expect(report).toContain('## 改进建议')
    })

    it('应该包含道路基本信息', () => {
      const roadData = createMockRoadData({
        name: '测试道路A',
        centerline: {
          points: [],
          totalLength: 1500,
          horizontalCurves: [],
          verticalCurves: []
        }
      })
      const results = createMockSafetyResults(1)
      const summary = {
        totalChecks: 1,
        passCount: 1,
        warningCount: 0,
        failCount: 0,
        safetyScore: 100,
        criticalIssues: []
      }
      
      const report = SafetyReportGenerator.generateReport(roadData, results, summary)
      
      expect(report).toContain('测试道路A')
      expect(report).toContain('1500m')
    })

    it('应该包含安全评分信息', () => {
      const roadData = createMockRoadData()
      const results = createMockSafetyResults(2)
      const summary = {
        totalChecks: 2,
        passCount: 1,
        warningCount: 1,
        failCount: 0,
        safetyScore: 85,
        criticalIssues: []
      }
      
      const report = SafetyReportGenerator.generateReport(roadData, results, summary)
      
      expect(report).toContain('85分')
      expect(report).toContain('通过: 1项')
      expect(report).toContain('警告: 1项')
    })

    it('应该包含关键问题详情', () => {
      const roadData = createMockRoadData()
      const criticalResult = {
        type: 'grade',
        status: 'fail',
        value: 12,
        standard: 8,
        message: '纵坡过大，存在安全隐患',
        location: { station: 150, coordinates: [116.3974, 39.9093, 50] }
      } as any
      
      const results = [criticalResult]
      const summary = {
        totalChecks: 1,
        passCount: 0,
        warningCount: 0,
        failCount: 1,
        safetyScore: 0,
        criticalIssues: [criticalResult]
      }
      
      const report = SafetyReportGenerator.generateReport(roadData, results, summary)
      
      expect(report).toContain('## 关键问题')
      expect(report).toContain('纵坡过大，存在安全隐患')
      expect(report).toContain('K150')
    })

    it('应该包含改进建议', () => {
      const roadData = createMockRoadData()
      const results = [
        { type: 'grade', status: 'fail', value: 12, standard: 8, message: 'fail', location: null }
      ] as any[]
      const summary = {
        totalChecks: 1,
        passCount: 0,
        warningCount: 0,
        failCount: 1,
        safetyScore: 0,
        criticalIssues: results
      }
      
      const report = SafetyReportGenerator.generateReport(roadData, results, summary)
      
      expect(report).toContain('## 改进建议')
      expect(report).toContain('### 紧急改进项目')
    })
  })
})

describe('性能测试', () => {
  it('安全检测应该在合理时间内完成', async () => {
    const config = {
      roadType: 'main',
      strictness: 'normal',
      checkItems: ['grade', 'radius', 'sight_distance', 'width']
    } as SafetyCheckConfig
    
    const safetyChecker = new RoadSafetyChecker(config)
    const roadData = createMockRoadData()
    
    const startTime = performance.now()
    await safetyChecker.performSafetyCheck(roadData)
    const endTime = performance.now()
    
    const executionTime = endTime - startTime
    expect(executionTime).toBeLessThan(1000) // 应该在1秒内完成
  })

  it('报告生成应该高效处理大量结果', () => {
    const roadData = createMockRoadData()
    const results = createMockSafetyResults(100) // 大量检测结果
    const summary = {
      totalChecks: 100,
      passCount: 70,
      warningCount: 20,
      failCount: 10,
      safetyScore: 80,
      criticalIssues: results.filter(r => r.status === 'fail')
    }
    
    const startTime = performance.now()
    const report = SafetyReportGenerator.generateReport(roadData, results, summary)
    const endTime = performance.now()
    
    const executionTime = endTime - startTime
    expect(executionTime).toBeLessThan(500) // 应该在0.5秒内完成
    expect(report).toBeDefined()
    expect(report.length).toBeGreaterThan(0)
  })
})
