/**
 * 性能优化工具
 * 提供各种性能优化功能，包括内存管理、计算优化、渲染优化等
 */

import { ref, reactive, nextTick } from 'vue'

// 性能监控数据接口
export interface PerformanceMetrics {
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }
  renderTime: number
  computeTime: number
  networkLatency: number
  fps: number
  lastUpdate: number
}

// 优化配置接口
export interface OptimizationConfig {
  enableMemoryOptimization: boolean
  enableRenderOptimization: boolean
  enableComputeOptimization: boolean
  enableNetworkOptimization: boolean
  maxCacheSize: number
  renderThrottleMs: number
  computeWorkerCount: number
}

/**
 * 性能优化器主类
 */
export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer
  private config: OptimizationConfig
  private metrics: PerformanceMetrics
  private cacheMap: Map<string, { data: any; timestamp: number; size: number }> = new Map()
  private renderQueue: Array<() => void> = []
  private computeWorkers: Worker[] = []
  private isRenderScheduled = false
  private lastRenderTime = 0

  private constructor() {
    this.config = {
      enableMemoryOptimization: true,
      enableRenderOptimization: true,
      enableComputeOptimization: true,
      enableNetworkOptimization: true,
      maxCacheSize: 100 * 1024 * 1024, // 100MB
      renderThrottleMs: 16, // 60fps
      computeWorkerCount: navigator.hardwareConcurrency || 4
    }

    this.metrics = reactive({
      memoryUsage: { used: 0, total: 0, percentage: 0 },
      renderTime: 0,
      computeTime: 0,
      networkLatency: 0,
      fps: 60,
      lastUpdate: Date.now()
    })

    this.initialize()
  }

  public static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer()
    }
    return PerformanceOptimizer.instance
  }

  /**
   * 初始化优化器
   */
  private initialize() {
    this.setupMemoryMonitoring()
    this.setupRenderOptimization()
    this.setupComputeOptimization()
    this.setupNetworkOptimization()
  }

  /**
   * 设置内存监控
   */
  private setupMemoryMonitoring() {
    if (!this.config.enableMemoryOptimization) return

    // 定期监控内存使用情况
    setInterval(() => {
      this.updateMemoryMetrics()
      this.cleanupCache()
    }, 5000)
  }

  /**
   * 更新内存指标
   */
  private updateMemoryMetrics() {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory
      this.metrics.memoryUsage = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
      }
    }
  }

  /**
   * 清理缓存
   */
  private cleanupCache() {
    const now = Date.now()
    const maxAge = 30 * 60 * 1000 // 30分钟
    let totalSize = 0

    // 计算总缓存大小
    for (const [key, value] of this.cacheMap) {
      totalSize += value.size
    }

    // 如果超过最大缓存大小，清理旧数据
    if (totalSize > this.config.maxCacheSize) {
      const entries = Array.from(this.cacheMap.entries())
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp)

      // 删除最旧的50%数据
      const deleteCount = Math.floor(entries.length * 0.5)
      for (let i = 0; i < deleteCount; i++) {
        this.cacheMap.delete(entries[i][0])
      }
    }

    // 清理过期数据
    for (const [key, value] of this.cacheMap) {
      if (now - value.timestamp > maxAge) {
        this.cacheMap.delete(key)
      }
    }
  }

  /**
   * 设置渲染优化
   */
  private setupRenderOptimization() {
    if (!this.config.enableRenderOptimization) return

    // 监控FPS
    let frameCount = 0
    let lastTime = performance.now()

    const countFPS = () => {
      frameCount++
      const currentTime = performance.now()
      
      if (currentTime - lastTime >= 1000) {
        this.metrics.fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        frameCount = 0
        lastTime = currentTime
      }
      
      requestAnimationFrame(countFPS)
    }
    
    requestAnimationFrame(countFPS)
  }

  /**
   * 设置计算优化
   */
  private setupComputeOptimization() {
    if (!this.config.enableComputeOptimization) return

    // 初始化Web Workers（如果支持）
    if (typeof Worker !== 'undefined') {
      for (let i = 0; i < this.config.computeWorkerCount; i++) {
        try {
          // 这里应该创建实际的Worker，但在测试环境中可能不可用
          // const worker = new Worker('/workers/compute-worker.js')
          // this.computeWorkers.push(worker)
        } catch (error) {
          console.warn('无法创建Web Worker:', error)
        }
      }
    }
  }

  /**
   * 设置网络优化
   */
  private setupNetworkOptimization() {
    if (!this.config.enableNetworkOptimization) return

    // 监控网络延迟
    this.measureNetworkLatency()
    setInterval(() => {
      this.measureNetworkLatency()
    }, 30000) // 每30秒测量一次
  }

  /**
   * 测量网络延迟
   */
  private async measureNetworkLatency() {
    try {
      const startTime = performance.now()
      await fetch('/api/ping', { method: 'HEAD' }).catch(() => {
        // 忽略错误，使用默认值
      })
      const endTime = performance.now()
      this.metrics.networkLatency = endTime - startTime
    } catch (error) {
      this.metrics.networkLatency = 0
    }
  }

  /**
   * 缓存数据
   */
  public cacheData(key: string, data: any, estimatedSize: number = 1024): void {
    this.cacheMap.set(key, {
      data,
      timestamp: Date.now(),
      size: estimatedSize
    })
  }

  /**
   * 获取缓存数据
   */
  public getCachedData(key: string): any {
    const cached = this.cacheMap.get(key)
    if (cached) {
      // 更新访问时间
      cached.timestamp = Date.now()
      return cached.data
    }
    return null
  }

  /**
   * 清除指定缓存
   */
  public clearCache(key?: string): void {
    if (key) {
      this.cacheMap.delete(key)
    } else {
      this.cacheMap.clear()
    }
  }

  /**
   * 节流渲染
   */
  public throttleRender(renderFn: () => void): void {
    this.renderQueue.push(renderFn)
    
    if (!this.isRenderScheduled) {
      this.isRenderScheduled = true
      
      const now = performance.now()
      const timeSinceLastRender = now - this.lastRenderTime
      const delay = Math.max(0, this.config.renderThrottleMs - timeSinceLastRender)
      
      setTimeout(() => {
        const startTime = performance.now()
        
        // 执行所有排队的渲染任务
        while (this.renderQueue.length > 0) {
          const renderFn = this.renderQueue.shift()
          if (renderFn) {
            renderFn()
          }
        }
        
        const endTime = performance.now()
        this.metrics.renderTime = endTime - startTime
        this.lastRenderTime = endTime
        this.isRenderScheduled = false
      }, delay)
    }
  }

  /**
   * 异步计算
   */
  public async computeAsync<T>(
    computeFn: () => T,
    useWorker: boolean = false
  ): Promise<T> {
    const startTime = performance.now()
    
    try {
      let result: T
      
      if (useWorker && this.computeWorkers.length > 0) {
        // 使用Web Worker进行计算（简化实现）
        result = await new Promise((resolve, reject) => {
          setTimeout(() => {
            try {
              resolve(computeFn())
            } catch (error) {
              reject(error)
            }
          }, 0)
        })
      } else {
        // 使用setTimeout避免阻塞UI
        result = await new Promise((resolve, reject) => {
          setTimeout(() => {
            try {
              resolve(computeFn())
            } catch (error) {
              reject(error)
            }
          }, 0)
        })
      }
      
      const endTime = performance.now()
      this.metrics.computeTime = endTime - startTime
      
      return result
    } catch (error) {
      const endTime = performance.now()
      this.metrics.computeTime = endTime - startTime
      throw error
    }
  }

  /**
   * 批量处理数据
   */
  public async processBatch<T, R>(
    items: T[],
    processFn: (item: T) => R,
    batchSize: number = 100
  ): Promise<R[]> {
    const results: R[] = []
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize)
      const batchResults = await this.computeAsync(() => 
        batch.map(processFn)
      )
      results.push(...batchResults)
      
      // 让出控制权，避免长时间阻塞
      await nextTick()
    }
    
    return results
  }

  /**
   * 防抖函数
   */
  public debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: number | undefined
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId)
      timeoutId = window.setTimeout(() => func(...args), delay)
    }
  }

  /**
   * 节流函数
   */
  public throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let lastCallTime = 0
    
    return (...args: Parameters<T>) => {
      const now = Date.now()
      if (now - lastCallTime >= delay) {
        lastCallTime = now
        func(...args)
      }
    }
  }

  /**
   * 获取性能指标
   */
  public getMetrics(): PerformanceMetrics {
    this.metrics.lastUpdate = Date.now()
    return { ...this.metrics }
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 获取配置
   */
  public getConfig(): OptimizationConfig {
    return { ...this.config }
  }

  /**
   * 获取缓存统计
   */
  public getCacheStats(): {
    size: number
    count: number
    totalSize: number
  } {
    let totalSize = 0
    for (const [, value] of this.cacheMap) {
      totalSize += value.size
    }
    
    return {
      size: this.cacheMap.size,
      count: this.cacheMap.size,
      totalSize
    }
  }

  /**
   * 优化建议
   */
  public getOptimizationSuggestions(): string[] {
    const suggestions: string[] = []
    const metrics = this.getMetrics()
    
    if (metrics.memoryUsage.percentage > 80) {
      suggestions.push('内存使用率过高，建议清理缓存或减少数据加载')
    }
    
    if (metrics.fps < 30) {
      suggestions.push('帧率过低，建议启用渲染优化或减少渲染复杂度')
    }
    
    if (metrics.renderTime > 16) {
      suggestions.push('渲染时间过长，建议使用渲染节流或优化渲染逻辑')
    }
    
    if (metrics.computeTime > 100) {
      suggestions.push('计算时间过长，建议使用异步计算或Web Worker')
    }
    
    if (metrics.networkLatency > 1000) {
      suggestions.push('网络延迟过高，建议启用网络优化或使用缓存')
    }
    
    return suggestions
  }

  /**
   * 销毁优化器
   */
  public destroy(): void {
    // 清理Web Workers
    this.computeWorkers.forEach(worker => {
      worker.terminate()
    })
    this.computeWorkers = []
    
    // 清理缓存
    this.cacheMap.clear()
    
    // 清理渲染队列
    this.renderQueue = []
  }
}

// 导出单例实例
export const performanceOptimizer = PerformanceOptimizer.getInstance()

// 便捷函数
export function optimizeRender(renderFn: () => void): void {
  performanceOptimizer.throttleRender(renderFn)
}

export function optimizeCompute<T>(computeFn: () => T): Promise<T> {
  return performanceOptimizer.computeAsync(computeFn)
}

export function cacheResult(key: string, data: any, size?: number): void {
  performanceOptimizer.cacheData(key, data, size)
}

export function getCachedResult(key: string): any {
  return performanceOptimizer.getCachedData(key)
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  return performanceOptimizer.debounce(func, delay)
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  return performanceOptimizer.throttle(func, delay)
}
