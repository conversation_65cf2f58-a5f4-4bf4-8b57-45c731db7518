/**
 * 数据流管理器
 * 负责各功能模块间的数据传递和状态同步
 */

import { ref, reactive, watch, computed } from 'vue'
import type { 
  GISData, 
  RoadData, 
  SafetyCheckResult, 
  RoadCenterline,
  DataFlowEvent,
  ModuleState 
} from '@/types'
import { useGISStore } from '@/stores/gis'
import { useAppStore } from '@/stores/counter'

// 数据流事件类型
export type DataFlowEventType = 
  | 'data_imported'
  | 'data_updated' 
  | 'data_deleted'
  | 'road_designed'
  | 'safety_checked'
  | 'module_switched'
  | 'selection_changed'
  | 'view_changed'

// 数据流事件接口
export interface DataFlowEvent {
  type: DataFlowEventType
  source: string
  target?: string
  data: any
  timestamp: number
  metadata?: Record<string, any>
}

// 模块状态接口
export interface ModuleState {
  module: string
  active: boolean
  selectedData: string[]
  viewState: any
  lastUpdate: number
}

/**
 * 数据流管理器类
 */
export class DataFlowManager {
  private static instance: DataFlowManager
  private eventListeners: Map<DataFlowEventType, Set<Function>> = new Map()
  private moduleStates: Map<string, ModuleState> = new Map()
  private dataCache: Map<string, any> = new Map()
  private eventHistory: DataFlowEvent[] = []

  private constructor() {
    this.initializeModuleStates()
    this.setupGlobalWatchers()
  }

  public static getInstance(): DataFlowManager {
    if (!DataFlowManager.instance) {
      DataFlowManager.instance = new DataFlowManager()
    }
    return DataFlowManager.instance
  }

  /**
   * 初始化模块状态
   */
  private initializeModuleStates() {
    const modules = ['gis', 'data', 'road', 'safety', 'report', 'monitor']
    
    modules.forEach(module => {
      this.moduleStates.set(module, {
        module,
        active: false,
        selectedData: [],
        viewState: {},
        lastUpdate: Date.now()
      })
    })
  }

  /**
   * 设置全局监听器
   */
  private setupGlobalWatchers() {
    const appStore = useAppStore()
    const gisStore = useGISStore()

    // 监听模块切换
    watch(() => appStore.uiState.activeModule, (newModule, oldModule) => {
      this.handleModuleSwitch(oldModule, newModule)
    })

    // 监听GIS数据变化
    watch(() => gisStore.allData, (newData, oldData) => {
      this.handleDataChange('gis', newData, oldData)
    }, { deep: true })

    // 监听选中数据变化
    watch(() => gisStore.selectedData, (newSelection, oldSelection) => {
      this.handleSelectionChange(newSelection, oldSelection)
    })
  }

  /**
   * 注册事件监听器
   */
  public on(eventType: DataFlowEventType, callback: Function): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set())
    }
    this.eventListeners.get(eventType)!.add(callback)
  }

  /**
   * 移除事件监听器
   */
  public off(eventType: DataFlowEventType, callback: Function): void {
    const listeners = this.eventListeners.get(eventType)
    if (listeners) {
      listeners.delete(callback)
    }
  }

  /**
   * 触发事件
   */
  public emit(event: DataFlowEvent): void {
    // 记录事件历史
    this.eventHistory.push(event)
    if (this.eventHistory.length > 1000) {
      this.eventHistory.shift() // 保持历史记录在合理范围内
    }

    // 触发监听器
    const listeners = this.eventListeners.get(event.type)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(event)
        } catch (error) {
          console.error('数据流事件处理错误:', error)
        }
      })
    }

    // 更新模块状态
    this.updateModuleState(event)
  }

  /**
   * 处理模块切换
   */
  private handleModuleSwitch(oldModule: string, newModule: string) {
    // 更新模块状态
    if (oldModule) {
      const oldState = this.moduleStates.get(oldModule)
      if (oldState) {
        oldState.active = false
        oldState.lastUpdate = Date.now()
      }
    }

    if (newModule) {
      const newState = this.moduleStates.get(newModule)
      if (newState) {
        newState.active = true
        newState.lastUpdate = Date.now()
      }
    }

    // 触发模块切换事件
    this.emit({
      type: 'module_switched',
      source: oldModule || 'none',
      target: newModule,
      data: { oldModule, newModule },
      timestamp: Date.now()
    })

    // 同步数据到新模块
    this.syncDataToModule(newModule)
  }

  /**
   * 处理数据变化
   */
  private handleDataChange(source: string, newData: any, oldData: any) {
    this.emit({
      type: 'data_updated',
      source,
      data: { newData, oldData },
      timestamp: Date.now()
    })

    // 更新缓存
    this.updateDataCache(source, newData)

    // 通知相关模块
    this.notifyRelatedModules(source, newData)
  }

  /**
   * 处理选择变化
   */
  private handleSelectionChange(newSelection: string[], oldSelection: string[]) {
    this.emit({
      type: 'selection_changed',
      source: 'gis',
      data: { newSelection, oldSelection },
      timestamp: Date.now()
    })

    // 更新所有模块的选中状态
    this.syncSelectionToAllModules(newSelection)
  }

  /**
   * 同步数据到指定模块
   */
  private syncDataToModule(module: string) {
    const gisStore = useGISStore()
    const moduleState = this.moduleStates.get(module)
    
    if (!moduleState) return

    switch (module) {
      case 'road':
        // 同步道路相关数据
        this.syncRoadData()
        break
      case 'safety':
        // 同步安全检测相关数据
        this.syncSafetyData()
        break
      case 'data':
        // 同步数据管理相关数据
        this.syncDataManagementData()
        break
      case 'report':
        // 同步报告相关数据
        this.syncReportData()
        break
    }
  }

  /**
   * 同步道路数据
   */
  private syncRoadData() {
    const gisStore = useGISStore()
    const roadData = gisStore.allData.filter(data => data.type === 'road')
    
    this.emit({
      type: 'data_updated',
      source: 'gis',
      target: 'road',
      data: { roadData },
      timestamp: Date.now(),
      metadata: { syncType: 'road_data' }
    })
  }

  /**
   * 同步安全检测数据
   */
  private syncSafetyData() {
    const gisStore = useGISStore()
    const roadData = gisStore.allData.filter(data => data.type === 'road')
    const safetyResults = this.dataCache.get('safety_results') || []
    
    this.emit({
      type: 'data_updated',
      source: 'gis',
      target: 'safety',
      data: { roadData, safetyResults },
      timestamp: Date.now(),
      metadata: { syncType: 'safety_data' }
    })
  }

  /**
   * 同步数据管理数据
   */
  private syncDataManagementData() {
    const gisStore = useGISStore()
    
    this.emit({
      type: 'data_updated',
      source: 'gis',
      target: 'data',
      data: { allData: gisStore.allData },
      timestamp: Date.now(),
      metadata: { syncType: 'data_management' }
    })
  }

  /**
   * 同步报告数据
   */
  private syncReportData() {
    const reportData = {
      roadDesigns: this.dataCache.get('road_designs') || [],
      safetyResults: this.dataCache.get('safety_results') || [],
      gisData: this.dataCache.get('gis_data') || []
    }
    
    this.emit({
      type: 'data_updated',
      source: 'system',
      target: 'report',
      data: reportData,
      timestamp: Date.now(),
      metadata: { syncType: 'report_data' }
    })
  }

  /**
   * 同步选择到所有模块
   */
  private syncSelectionToAllModules(selection: string[]) {
    this.moduleStates.forEach((state, module) => {
      state.selectedData = [...selection]
      state.lastUpdate = Date.now()
    })
  }

  /**
   * 更新数据缓存
   */
  private updateDataCache(source: string, data: any) {
    this.dataCache.set(`${source}_data`, data)
    this.dataCache.set(`${source}_timestamp`, Date.now())
  }

  /**
   * 通知相关模块
   */
  private notifyRelatedModules(source: string, data: any) {
    const relatedModules = this.getRelatedModules(source)
    
    relatedModules.forEach(module => {
      this.emit({
        type: 'data_updated',
        source,
        target: module,
        data,
        timestamp: Date.now(),
        metadata: { relatedUpdate: true }
      })
    })
  }

  /**
   * 获取相关模块
   */
  private getRelatedModules(source: string): string[] {
    const moduleRelations: Record<string, string[]> = {
      'gis': ['road', 'safety', 'data', 'report'],
      'road': ['safety', 'report'],
      'safety': ['report'],
      'data': ['gis', 'road', 'safety']
    }
    
    return moduleRelations[source] || []
  }

  /**
   * 更新模块状态
   */
  private updateModuleState(event: DataFlowEvent) {
    if (event.target) {
      const state = this.moduleStates.get(event.target)
      if (state) {
        state.lastUpdate = event.timestamp
        if (event.metadata?.viewState) {
          state.viewState = { ...state.viewState, ...event.metadata.viewState }
        }
      }
    }
  }

  /**
   * 获取模块状态
   */
  public getModuleState(module: string): ModuleState | undefined {
    return this.moduleStates.get(module)
  }

  /**
   * 获取事件历史
   */
  public getEventHistory(limit?: number): DataFlowEvent[] {
    if (limit) {
      return this.eventHistory.slice(-limit)
    }
    return [...this.eventHistory]
  }

  /**
   * 清理缓存
   */
  public clearCache() {
    this.dataCache.clear()
    this.eventHistory.length = 0
  }

  /**
   * 获取缓存数据
   */
  public getCachedData(key: string): any {
    return this.dataCache.get(key)
  }

  /**
   * 设置缓存数据
   */
  public setCachedData(key: string, data: any): void {
    this.dataCache.set(key, data)
  }
}

// 导出单例实例
export const dataFlowManager = DataFlowManager.getInstance()

// 便捷的事件触发函数
export function emitDataFlowEvent(
  type: DataFlowEventType,
  source: string,
  data: any,
  target?: string,
  metadata?: Record<string, any>
) {
  dataFlowManager.emit({
    type,
    source,
    target,
    data,
    timestamp: Date.now(),
    metadata
  })
}

// 便捷的事件监听函数
export function onDataFlowEvent(
  eventType: DataFlowEventType,
  callback: (event: DataFlowEvent) => void
) {
  dataFlowManager.on(eventType, callback)
}

// 便捷的事件移除函数
export function offDataFlowEvent(
  eventType: DataFlowEventType,
  callback: (event: DataFlowEvent) => void
) {
  dataFlowManager.off(eventType, callback)
}
