<template>
  <header class="app-header">
    <!-- Logo和标题 -->
    <div class="header-left">
      <div class="logo">
        <el-icon size="24"><Location /></el-icon>
        <span class="logo-text">露天矿山道路设计</span>
      </div>
    </div>

    <!-- 主导航菜单 -->
    <nav class="nav-menu">
      <div
        v-for="module in modules"
        :key="module.key"
        class="nav-item"
        :class="{ active: currentModule === module.key }"
        @click="navigateToModule(module)"
      >
        <el-icon><component :is="module.icon" /></el-icon>
        <span>{{ module.name }}</span>
      </div>
    </nav>

    <!-- 右侧工具栏 -->
    <div class="header-right">
      <!-- 主题切换 -->
      <el-tooltip content="切换主题" placement="bottom">
        <el-button
          circle
          size="small"
          @click="toggleTheme"
        >
          <el-icon><Sunny v-if="appStore.isDarkTheme" /><Moon v-else /></el-icon>
        </el-button>
      </el-tooltip>

      <!-- 语言切换 -->
      <el-dropdown @command="handleLanguageChange">
        <el-button circle size="small">
          <el-icon><Globe /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="zh-CN">中文</el-dropdown-item>
            <el-dropdown-item command="en-US">English</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 设置 -->
      <el-tooltip content="设置" placement="bottom">
        <el-button circle size="small" @click="showSettings">
          <el-icon><Setting /></el-icon>
        </el-button>
      </el-tooltip>

      <!-- 帮助 -->
      <el-tooltip content="帮助" placement="bottom">
        <el-button circle size="small" @click="showHelp">
          <el-icon><QuestionFilled /></el-icon>
        </el-button>
      </el-tooltip>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '@/stores/counter'
import {
  Location,
  Sunny,
  Moon,
  Globe,
  Setting,
  QuestionFilled,
  MapLocation,
  DataAnalysis,
  Route,
  Shield,
  Files,
  Monitor
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()

// 功能模块配置
const modules = [
  { key: 'gis', name: 'GIS数据', icon: 'MapLocation', path: '/gis' },
  { key: 'data', name: '数据管理', icon: 'DataAnalysis', path: '/data' },
  { key: 'road', name: '道路设计', icon: 'Route', path: '/road' },
  { key: 'safety', name: '安全检测', icon: 'Shield', path: '/safety' },
  { key: 'report', name: '报告输出', icon: 'Files', path: '/report' },
  { key: 'monitor', name: '系统监控', icon: 'Monitor', path: '/monitor' }
]

// 当前活动模块
const currentModule = computed(() => {
  return route.name as string || 'gis'
})

// 导航到模块
function navigateToModule(module: any) {
  appStore.setActiveModule(module.key)
  router.push(module.path)
}

// 切换主题
function toggleTheme() {
  const newTheme = appStore.isDarkTheme ? 'light' : 'dark'
  appStore.setTheme(newTheme)
  ElMessage.success(`已切换到${newTheme === 'dark' ? '深色' : '浅色'}主题`)
}

// 语言切换
function handleLanguageChange(language: 'zh-CN' | 'en-US') {
  appStore.setLanguage(language)
  ElMessage.success(`语言已切换为${language === 'zh-CN' ? '中文' : 'English'}`)
}

// 显示设置对话框
function showSettings() {
  ElMessage.info('设置功能开发中...')
}

// 显示帮助
function showHelp() {
  ElMessage.info('帮助文档开发中...')
}
</script>

<style scoped>
.app-header {
  height: var(--header-height);
  background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%);
  border-bottom: 2px solid var(--color-primary);
  color: var(--color-text);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  box-shadow: var(--shadow-2);
  position: relative;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: bold;
  color: var(--color-primary);
}

.logo-text {
  font-size: var(--font-size-lg);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
  justify-content: center;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--color-text-soft);
  font-size: var(--font-size-sm);
  white-space: nowrap;
}

.nav-item:hover {
  background-color: var(--color-background-soft);
  color: var(--color-text);
  transform: translateY(-1px);
}

.nav-item.active {
  background-color: var(--color-primary);
  color: #000000;
  font-weight: bold;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.header-right .el-button {
  background-color: transparent;
  border-color: var(--color-border);
  color: var(--color-text-soft);
}

.header-right .el-button:hover {
  background-color: var(--color-background-soft);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .nav-menu {
    gap: var(--spacing-sm);
  }

  .nav-item span {
    display: none;
  }

  .logo-text {
    display: none;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 0 var(--spacing-md);
  }

  .nav-menu {
    gap: var(--spacing-xs);
  }

  .header-right {
    gap: var(--spacing-xs);
  }
}
</style>
