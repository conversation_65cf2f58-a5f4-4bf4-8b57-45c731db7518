<template>
  <div class="app-header">
    <div class="header-left">
      <h1 class="app-title">露天矿山道路设计系统</h1>
    </div>
    <div class="header-center">
      <el-menu
        :default-active="activeIndex"
        class="header-menu"
        mode="horizontal"
        @select="handleSelect"
        background-color="#2c2c2c"
        text-color="#ffffff"
        active-text-color="#ffd04b"
      >
        <el-menu-item index="gis">GIS数据管理</el-menu-item>
        <el-menu-item index="road-design">道路设计</el-menu-item>
        <el-menu-item index="road-analysis">道路分析</el-menu-item>
        <el-menu-item index="safety">安全检测</el-menu-item>
        <el-menu-item index="data-import">数据导入导出</el-menu-item>
      </el-menu>
    </div>
    <div class="header-right">
      <el-button type="primary" size="small">
        <el-icon><Setting /></el-icon>
        设置
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const activeIndex = ref('gis')

const handleSelect = (key: string) => {
  activeIndex.value = key
  router.push(`/${key}`)
}
</script>

<style scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
  border-bottom: 2px solid #ffd04b;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.header-left {
  flex: 1;
}

.app-title {
  color: #ffffff;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.header-center {
  flex: 2;
  display: flex;
  justify-content: center;
}

.header-menu {
  border-bottom: none;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.el-button {
  background-color: #ffd04b;
  border-color: #ffd04b;
  color: #2c2c2c;
}

.el-button:hover {
  background-color: #ffdc6b;
  border-color: #ffdc6b;
}
</style>
