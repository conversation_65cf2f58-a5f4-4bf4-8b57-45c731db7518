<template>
  <aside class="app-sidebar" :class="{ collapsed }">
    <!-- 折叠按钮 -->
    <div class="sidebar-header">
      <el-button 
        circle 
        size="small" 
        @click="$emit('toggle')"
        class="collapse-btn"
      >
        <el-icon><Expand v-if="collapsed" /><Fold v-else /></el-icon>
      </el-button>
      <span v-if="!collapsed" class="sidebar-title">数据操作区</span>
    </div>

    <!-- 动态内容区域 -->
    <div class="sidebar-content">
      <!-- GIS数据管理模块 -->
      <div v-if="appStore.uiState.activeModule === 'gis'" class="module-content">
        <GISDataManager />
      </div>

      <!-- 数据管理模块 -->
      <div v-else-if="appStore.uiState.activeModule === 'data'" class="module-content">
        <DataManagerSidebar />
      </div>

      <!-- 道路设计模块 -->
      <div v-else-if="appStore.uiState.activeModule === 'road'" class="module-content">
        <RoadDesignSidebar />
      </div>

      <!-- 安全检测模块 -->
      <div v-else-if="appStore.uiState.activeModule === 'safety'" class="module-content">
        <SafetyCheckPanel />
      </div>

      <!-- 报告输出模块 -->
      <div v-else-if="appStore.uiState.activeModule === 'report'" class="module-content">
        <ReportPanel />
      </div>

      <!-- 系统监控模块 -->
      <div v-else-if="appStore.uiState.activeModule === 'monitor'" class="module-content">
        <SystemMonitor />
      </div>

      <!-- 默认内容 -->
      <div v-else class="module-content">
        <el-empty description="请选择功能模块" />
      </div>
    </div>

    <!-- 数据输出显示区 -->
    <div class="sidebar-footer" v-if="!collapsed">
      <div class="output-section">
        <div class="section-title">
          <el-icon><DataLine /></el-icon>
          <span>数据输出</span>
        </div>
        <div class="output-content">
          <el-scrollbar height="200px">
            <div class="output-item" v-for="item in outputData" :key="item.id">
              <div class="output-header">
                <span class="output-name">{{ item.name }}</span>
                <el-tag :type="getStatusType(item.status)" size="small">
                  {{ item.status }}
                </el-tag>
              </div>
              <div class="output-details">
                <span class="output-time">{{ formatTime(item.time) }}</span>
                <span class="output-size">{{ item.size }}</span>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAppStore } from '@/stores/counter'
import { Expand, Fold, DataLine } from '@element-plus/icons-vue'
import GISDataManager from '@/components/GIS/GISDataManager.vue'
import DataManagerSidebar from '@/components/data/DataManagerSidebar.vue'
import RoadDesignSidebar from '@/components/road/RoadDesignSidebar.vue'
import SafetyCheckSidebar from '@/components/safety/SafetyCheckSidebar.vue'

// 临时组件占位符
const RoadDesignPanel = { template: '<div class="placeholder">道路设计模块开发中...</div>' }
const SafetyCheckPanel = { template: '<div class="placeholder">安全检测模块开发中...</div>' }
const ReportPanel = { template: '<div class="placeholder">报告输出模块开发中...</div>' }
const SystemMonitor = { template: '<div class="placeholder">系统监控模块开发中...</div>' }

interface Props {
  collapsed: boolean
}

defineProps<Props>()
defineEmits<{
  toggle: []
}>()

const appStore = useAppStore()

// 模拟输出数据
const outputData = ref([
  {
    id: '1',
    name: '地形分析报告',
    status: 'completed',
    time: new Date(Date.now() - 1000 * 60 * 30),
    size: '2.5MB'
  },
  {
    id: '2',
    name: '道路设计图',
    status: 'processing',
    time: new Date(Date.now() - 1000 * 60 * 10),
    size: '1.8MB'
  },
  {
    id: '3',
    name: '安全检测结果',
    status: 'failed',
    time: new Date(Date.now() - 1000 * 60 * 5),
    size: '0.5MB'
  }
])

function getStatusType(status: string) {
  switch (status) {
    case 'completed': return 'success'
    case 'processing': return 'warning'
    case 'failed': return 'danger'
    default: return 'info'
  }
}

function formatTime(time: Date) {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  return `${days}天前`
}
</script>

<style scoped>
.app-sidebar {
  width: var(--sidebar-width);
  background-color: var(--color-background-soft);
  border-right: 1px solid var(--color-border);
  height: calc(100vh - var(--header-height));
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  overflow: hidden;
}

.app-sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-background-mute);
}

.collapse-btn {
  background-color: transparent;
  border-color: var(--color-border);
  color: var(--color-text-soft);
}

.collapse-btn:hover {
  background-color: var(--color-background-soft);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.sidebar-title {
  margin-left: var(--spacing-md);
  font-weight: bold;
  color: var(--color-primary);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
}

.module-content {
  height: 100%;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-text-soft);
  font-style: italic;
  background-color: var(--color-background);
  border: 2px dashed var(--color-border);
  border-radius: var(--border-radius-base);
}

.sidebar-footer {
  border-top: 1px solid var(--color-border);
  background-color: var(--color-background-mute);
}

.output-section {
  padding: var(--spacing-md);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
  font-weight: bold;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
}

.output-content {
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-sm);
}

.output-item {
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--color-border);
  transition: background-color 0.3s ease;
}

.output-item:last-child {
  border-bottom: none;
}

.output-item:hover {
  background-color: var(--color-background-soft);
}

.output-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.output-name {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  font-weight: 500;
}

.output-details {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

/* 折叠状态下的样式 */
.app-sidebar.collapsed .sidebar-content,
.app-sidebar.collapsed .sidebar-footer {
  display: none;
}

.app-sidebar.collapsed .sidebar-header {
  justify-content: center;
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar,
.output-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track,
.output-content::-webkit-scrollbar-track {
  background: var(--color-background);
}

.sidebar-content::-webkit-scrollbar-thumb,
.output-content::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover,
.output-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}
</style>
