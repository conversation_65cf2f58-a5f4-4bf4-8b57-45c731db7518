@echo off
chcp 65001 >nul
title 露天矿山道路设计软件 - 调试模式

:: 获取脚本所在目录并切换到项目目录
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

echo.
echo ========================================
echo   露天矿山道路设计软件调试启动器 v1.0
echo ========================================
echo.

:: 设置调试环境变量
set NODE_ENV=development
set VITE_DEBUG_MODE=true
set VITE_LOG_LEVEL=debug
set VITE_SHOW_PERFORMANCE_MONITOR=true
set VITE_ENABLE_DEVTOOLS=true

echo 🔧 调试模式配置:
echo    - 调试模式: 启用
echo    - 日志级别: DEBUG
echo    - 性能监控: 启用
echo    - 开发工具: 启用
echo    - 热重载: 启用
echo    - 项目目录: %SCRIPT_DIR%
echo.

:: 快速检查
if not exist "package.json" (
    echo ❌ 错误: 未找到package.json文件
    echo    当前目录: %CD%
    echo    请确保在正确的项目目录运行此脚本
    pause
    exit /b 1
)

:: 检查依赖
if not exist "node_modules" (
    echo 📦 正在安装项目依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

:: 清理缓存
echo 🧹 清理开发缓存...
if exist ".vite" rmdir /s /q ".vite" 2>nul
if exist "dist" rmdir /s /q "dist" 2>nul
if exist "node_modules\.vite" rmdir /s /q "node_modules\.vite" 2>nul

:: 启动类型检查（后台）
echo 🔍 启动TypeScript类型检查...
start "TypeScript检查" /min cmd /c "npm run type-check -- --watch"

:: 等待一秒让类型检查启动
timeout /t 2 /nobreak >nul

:: 启动测试监控（后台）
echo 🧪 启动测试监控...
start "测试监控" /min cmd /c "npm run test:watch"

:: 等待一秒让测试启动
timeout /t 2 /nobreak >nul

:: 启动开发服务器
echo 🚀 启动调试开发服务器...
echo.
echo 📊 调试信息:
echo    ┌─────────────────────────────────────────┐
echo    │  主服务器: http://localhost:3000       │
echo    │  测试界面: http://localhost:51204/__vitest__/ │
echo    │  性能监控: 浏览器开发者工具             │
echo    └─────────────────────────────────────────┘
echo.
echo 💡 调试提示:
echo    - 打开浏览器开发者工具查看详细日志
echo    - 使用 Vue DevTools 扩展调试组件
echo    - 查看网络面板监控API请求
echo    - 使用性能面板分析性能问题
echo    - 查看控制台的详细调试信息
echo.
echo 按 Ctrl+C 停止所有服务
echo.

:: 启动主服务器
npm run dev

echo.
echo ⚠️  调试服务器已停止
echo 正在关闭相关进程...

:: 尝试关闭相关进程
taskkill /f /im node.exe 2>nul
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq TypeScript检查*" 2>nul
taskkill /f /im cmd.exe /fi "WINDOWTITLE eq 测试监控*" 2>nul

echo.
echo 💡 调试会话结束
echo    如需重新调试，请再次运行此脚本
echo.
pause
