<template>
  <div class="monitor-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>系统监控中心</h2>
      <p class="page-description">
        实时监控系统运行状态、数据流处理、模块集成状态和性能指标
      </p>
    </div>

    <!-- 功能模块选择 -->
    <div class="module-selector">
      <el-card>
        <div class="module-tabs">
          <el-tabs v-model="activeModule" @tab-change="handleModuleChange">
            <el-tab-pane label="数据流监控" name="dataflow">
              <template #label>
                <div class="tab-label">
                  <el-icon><DataAnalysis /></el-icon>
                  <span>数据流监控</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="性能监控" name="performance">
              <template #label>
                <div class="tab-label">
                  <el-icon><Monitor /></el-icon>
                  <span>性能监控</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="错误日志" name="logs">
              <template #label>
                <div class="tab-label">
                  <el-icon><Warning /></el-icon>
                  <span>错误日志</span>
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane label="用户活动" name="activity">
              <template #label>
                <div class="tab-label">
                  <el-icon><User /></el-icon>
                  <span>用户活动</span>
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>

    <!-- 模块内容区 -->
    <div class="module-content">
      <!-- 数据流监控模块 -->
      <div v-if="activeModule === 'dataflow'" class="content-panel">
        <DataFlowMonitor />
      </div>

      <!-- 性能监控模块 -->
      <div v-else-if="activeModule === 'performance'" class="content-panel">
        <PerformanceMonitor />
      </div>

      <!-- 错误日志模块 -->
      <div v-else-if="activeModule === 'logs'" class="content-panel">
        <ErrorLogMonitor />
      </div>

      <!-- 用户活动模块 -->
      <div v-else-if="activeModule === 'activity'" class="content-panel">
        <UserActivityMonitor />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  DataAnalysis,
  Monitor,
  Warning,
  User
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import DataFlowMonitor from '@/components/system/DataFlowMonitor.vue'

// 临时组件占位符
const PerformanceMonitor = {
  template: '<div class="placeholder">性能监控模块开发中...</div>'
}
const ErrorLogMonitor = {
  template: '<div class="placeholder">错误日志模块开发中...</div>'
}
const UserActivityMonitor = {
  template: '<div class="placeholder">用户活动模块开发中...</div>'
}

// 响应式数据
const activeModule = ref('dataflow')

// 方法
function handleModuleChange(moduleName: string) {
  ElMessage.info(`已切换到${getModuleName(moduleName)}模块`)
}

function getModuleName(module: string): string {
  const names = {
    dataflow: '数据流监控',
    performance: '性能监控',
    logs: '错误日志',
    activity: '用户活动'
  }
  return names[module as keyof typeof names] || module
}
</script>

<style scoped>
.monitor-view {
  padding: var(--spacing-lg);
  color: var(--color-text);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.page-header {
  margin-bottom: var(--spacing-md);
}

.page-header h2 {
  color: var(--color-primary);
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-2xl);
}

.page-description {
  color: var(--color-text-soft);
  margin: 0;
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.module-selector {
  flex-shrink: 0;
}

.module-tabs {
  margin: -20px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.module-content {
  flex: 1;
  overflow: hidden;
}

.content-panel {
  height: 100%;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  background-color: var(--color-background-soft);
  border: 2px dashed var(--color-border);
  border-radius: var(--border-radius-base);
  color: var(--color-text-soft);
  font-style: italic;
  font-size: var(--font-size-lg);
}

/* Element Plus 样式覆盖 */
:deep(.el-tabs__header) {
  margin: 0;
  background-color: var(--color-background-mute);
  border-radius: var(--border-radius-base) var(--border-radius-base) 0 0;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0 var(--spacing-md);
}

:deep(.el-tabs__item) {
  color: var(--color-text-soft);
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: var(--color-primary);
}

:deep(.el-tabs__active-bar) {
  background-color: var(--color-primary);
}

:deep(.el-tabs__content) {
  padding: var(--spacing-lg);
}

:deep(.el-card) {
  background-color: var(--color-background-soft);
  border-color: var(--color-border);
}
</style>
