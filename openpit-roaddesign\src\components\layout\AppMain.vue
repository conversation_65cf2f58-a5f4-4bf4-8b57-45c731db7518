<template>
  <main class="app-main">
    <!-- 工具栏 -->
    <div class="main-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button size="small" @click="resetView">
            <el-icon><Refresh /></el-icon>
            重置视图
          </el-button>
          <el-button size="small" @click="fitToData">
            <el-icon><FullScreen /></el-icon>
            适应数据
          </el-button>
          <el-button size="small" @click="toggleMeasure">
            <el-icon><Ruler /></el-icon>
            测量工具
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-center">
        <el-select v-model="currentView" size="small" style="width: 120px">
          <el-option label="三维视图" value="3d" />
          <el-option label="二维视图" value="2d" />
          <el-option label="剖面视图" value="profile" />
        </el-select>
      </div>

      <div class="toolbar-right">
        <el-button size="small" @click="takeScreenshot">
          <el-icon><Camera /></el-icon>
          截图
        </el-button>
        <el-button size="small" @click="exportView">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- Cesium 3D地球容器 -->
    <div class="cesium-container" ref="cesiumContainer">
      <!-- 加载指示器 -->
      <div v-if="loading" class="loading-overlay">
        <el-loading-spinner />
        <p>{{ loadingText || '加载中...' }}</p>
      </div>
    </div>

    <!-- 右侧悬浮工具栏 -->
    <div class="floating-toolbar">
      <!-- 视图控制 -->
      <div class="toolbar-group">
        <el-tooltip content="放大" placement="left">
          <el-button circle size="small" @click="zoomIn">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="缩小" placement="left">
          <el-button circle size="small" @click="zoomOut">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="回到初始位置" placement="left">
          <el-button circle size="small" @click="goHome">
            <el-icon><HomeFilled /></el-icon>
          </el-button>
        </el-tooltip>
      </div>

      <!-- 图层控制 -->
      <div class="toolbar-group">
        <el-tooltip content="图层管理" placement="left">
          <el-button circle size="small" @click="toggleLayerPanel">
            <el-icon><Grid /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="地形开关" placement="left">
          <el-button 
            circle 
            size="small" 
            :type="terrainEnabled ? 'primary' : 'default'"
            @click="toggleTerrain"
          >
            <el-icon><Mountain /></el-icon>
          </el-button>
        </el-tooltip>
      </div>

      <!-- 分析工具 -->
      <div class="toolbar-group">
        <el-tooltip content="距离测量" placement="left">
          <el-button circle size="small" @click="measureDistance">
            <el-icon><Ruler /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="面积测量" placement="left">
          <el-button circle size="small" @click="measureArea">
            <el-icon><Crop /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="高程查询" placement="left">
          <el-button circle size="small" @click="queryElevation">
            <el-icon><TrendCharts /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span class="status-item">坐标: {{ currentCoordinates }}</span>
        <span class="status-item">高程: {{ currentElevation }}m</span>
        <span class="status-item">比例尺: 1:{{ currentScale }}</span>
      </div>
      <div class="status-right">
        <span class="status-item">数据加载: {{ dataLoadStatus }}</span>
        <span class="status-item">渲染FPS: {{ renderFPS }}</span>
      </div>
    </div>

    <!-- 图层面板 -->
    <el-drawer
      v-model="layerPanelVisible"
      title="图层管理"
      direction="rtl"
      size="300px"
    >
      <LayerPanel />
    </el-drawer>
  </main>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useAppStore } from '@/stores/counter'
import { useGISStore } from '@/stores/gis'
import {
  Refresh,
  FullScreen,
  Ruler,
  Camera,
  Download,
  ZoomIn,
  ZoomOut,
  HomeFilled,
  Grid,
  Mountain,
  Crop,
  TrendCharts
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as Cesium from 'cesium'
import { CESIUM_CONFIG } from '@/config'
import LayerPanel from '@/components/common/LayerPanel.vue'

const appStore = useAppStore()
const gisStore = useGISStore()

// 响应式数据
const cesiumContainer = ref<HTMLElement>()
const loading = ref(true)
const loadingText = ref('初始化Cesium...')
const currentView = ref('3d')
const layerPanelVisible = ref(false)
const terrainEnabled = ref(true)

// 状态栏数据
const currentCoordinates = ref('116.3974°, 39.9093°')
const currentElevation = ref(50)
const currentScale = ref(100000)
const dataLoadStatus = ref('就绪')
const renderFPS = ref(60)

// Cesium viewer实例
let viewer: Cesium.Viewer | null = null

onMounted(async () => {
  await initCesium()
})

onUnmounted(() => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})

// 初始化Cesium
async function initCesium() {
  try {
    loadingText.value = '创建Cesium视图...'
    
    if (!cesiumContainer.value) {
      throw new Error('Cesium容器未找到')
    }

    // 创建Cesium viewer
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      ...CESIUM_CONFIG.viewer,
      terrainProvider: await Cesium.createWorldTerrainAsync({
        requestVertexNormals: true,
        requestWaterMask: true
      })
    })

    // 设置初始视图
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(116.3974, 39.9093, 1000),
      orientation: {
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-45),
        roll: 0.0
      }
    })

    // 添加鼠标移动事件监听
    viewer.canvas.addEventListener('mousemove', updateMousePosition)

    // 添加相机移动事件监听
    viewer.camera.moveEnd.addEventListener(updateCameraInfo)

    loadingText.value = '加载完成'
    loading.value = false
    
    ElMessage.success('Cesium初始化成功')
  } catch (error) {
    console.error('Cesium初始化失败:', error)
    ElMessage.error('Cesium初始化失败')
    loading.value = false
  }
}

// 更新鼠标位置信息
function updateMousePosition(event: MouseEvent) {
  if (!viewer) return

  const pick = viewer.camera.getPickRay(new Cesium.Cartesian2(event.clientX, event.clientY))
  if (!pick) return

  const globe = viewer.scene.globe
  const cartesian = globe.pick(pick, viewer.scene)
  if (!cartesian) return

  const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
  const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6)
  const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6)
  const height = cartographic.height.toFixed(1)

  currentCoordinates.value = `${longitude}°, ${latitude}°`
  currentElevation.value = parseFloat(height)
}

// 更新相机信息
function updateCameraInfo() {
  if (!viewer) return

  const camera = viewer.camera
  const height = camera.positionCartographic.height
  currentScale.value = Math.round(height / 10)
}

// 工具栏方法
function resetView() {
  if (!viewer) return
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(116.3974, 39.9093, 1000)
  })
}

function fitToData() {
  ElMessage.info('适应数据功能开发中...')
}

function toggleMeasure() {
  ElMessage.info('测量工具功能开发中...')
}

function takeScreenshot() {
  if (!viewer) return
  
  viewer.render()
  const canvas = viewer.scene.canvas
  const image = canvas.toDataURL('image/png')
  
  // 创建下载链接
  const link = document.createElement('a')
  link.download = `screenshot_${new Date().getTime()}.png`
  link.href = image
  link.click()
  
  ElMessage.success('截图已保存')
}

function exportView() {
  ElMessage.info('导出功能开发中...')
}

// 悬浮工具栏方法
function zoomIn() {
  if (!viewer) return
  viewer.camera.zoomIn(viewer.camera.positionCartographic.height * 0.5)
}

function zoomOut() {
  if (!viewer) return
  viewer.camera.zoomOut(viewer.camera.positionCartographic.height * 0.5)
}

function goHome() {
  resetView()
}

function toggleLayerPanel() {
  layerPanelVisible.value = !layerPanelVisible.value
}

function toggleTerrain() {
  terrainEnabled.value = !terrainEnabled.value
  if (viewer) {
    viewer.terrainProvider = terrainEnabled.value 
      ? Cesium.createWorldTerrain()
      : new Cesium.EllipsoidTerrainProvider()
  }
}

function measureDistance() {
  ElMessage.info('距离测量功能开发中...')
}

function measureArea() {
  ElMessage.info('面积测量功能开发中...')
}

function queryElevation() {
  ElMessage.info('高程查询功能开发中...')
}
</script>

<style scoped>
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
  position: relative;
}

.main-toolbar {
  height: var(--toolbar-height);
  background-color: var(--color-background-mute);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  gap: var(--spacing-md);
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.cesium-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-text);
  z-index: 1000;
}

.loading-overlay p {
  margin-top: var(--spacing-md);
  font-size: var(--font-size-lg);
}

.floating-toolbar {
  position: absolute;
  right: var(--spacing-md);
  top: calc(var(--toolbar-height) + var(--spacing-md));
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  z-index: 100;
}

.toolbar-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  background-color: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-xs);
  box-shadow: var(--shadow-2);
}

.toolbar-group .el-button {
  background-color: transparent;
  border-color: var(--color-border);
  color: var(--color-text-soft);
}

.toolbar-group .el-button:hover {
  background-color: var(--color-background-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.toolbar-group .el-button.el-button--primary {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: #000000;
}

.status-bar {
  height: 24px;
  background-color: var(--color-background-mute);
  border-top: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

.status-left,
.status-right {
  display: flex;
  gap: var(--spacing-md);
}

.status-item {
  white-space: nowrap;
}

/* Cesium样式覆盖 */
:deep(.cesium-viewer-bottom) {
  display: none;
}

:deep(.cesium-widget-credits) {
  display: none;
}
</style>
