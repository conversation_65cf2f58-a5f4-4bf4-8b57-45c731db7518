<template>
  <div class="data-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>数据管理系统</h2>
      <p class="page-description">
        管理地表数据、地形数据、钻孔数据、地质数据、AutoCAD数据的生成、导入、显示、修改、删除、导出功能
      </p>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon terrain">
                <el-icon><Mountain /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ gisStore.getDataCountByType('terrain') }}</div>
                <div class="stat-label">地形数据</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon drilling">
                <el-icon><Location /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ gisStore.getDataCountByType('drilling') }}</div>
                <div class="stat-label">钻孔数据</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon geology">
                <el-icon><Histogram /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ gisStore.getDataCountByType('geology') }}</div>
                <div class="stat-label">地质数据</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon cad">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ gisStore.getDataCountByType('cad') }}</div>
                <div class="stat-label">CAD数据</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据管理器 -->
    <div class="data-manager-container">
      <el-card>
        <DataManager />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useGISStore } from '@/stores/gis'
import { Mountain, Location, Histogram, Document } from '@element-plus/icons-vue'
import DataManager from '@/components/data/DataManager.vue'

const gisStore = useGISStore()
</script>

<style scoped>
.data-view {
  padding: var(--spacing-lg);
  color: var(--color-text);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: var(--spacing-lg);
}

.page-header h2 {
  color: var(--color-primary);
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-2xl);
}

.page-description {
  color: var(--color-text-soft);
  margin: 0;
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.stats-cards {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-3);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: var(--spacing-md);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  font-size: 24px;
  color: white;
}

.stat-icon.terrain {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stat-icon.drilling {
  background: linear-gradient(135deg, #e6a23c, #f0c78a);
}

.stat-icon.geology {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stat-icon.cad {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: bold;
  color: var(--color-text);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
  margin-top: var(--spacing-xs);
}

.data-manager-container {
  flex: 1;
  overflow: hidden;
}

.data-manager-container .el-card {
  height: 100%;
}

:deep(.data-manager-container .el-card__body) {
  height: 100%;
  padding: 0;
}

/* Element Plus 样式覆盖 */
:deep(.el-card) {
  background-color: var(--color-background-soft);
  border-color: var(--color-border);
}

:deep(.el-card__body) {
  color: var(--color-text);
}
</style>
