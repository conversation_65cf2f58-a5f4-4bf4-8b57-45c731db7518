#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "========================================"
echo "   露天矿山道路设计软件启动器 v1.0"
echo "========================================"
echo -e "${NC}"

# 检查Node.js是否安装
echo -e "[1/6] 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误: 未检测到Node.js，请先安装Node.js${NC}"
    echo "   下载地址: https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node --version)
echo -e "${GREEN}✅ Node.js版本: $NODE_VERSION${NC}"

# 检查npm是否可用
echo -e "[2/6] 检查npm包管理器..."
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ 错误: npm不可用${NC}"
    exit 1
fi

NPM_VERSION=$(npm --version)
echo -e "${GREEN}✅ npm版本: $NPM_VERSION${NC}"

# 检查项目目录
echo -e "[3/6] 检查项目文件..."
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ 错误: 未找到package.json文件，请确保在项目根目录运行此脚本${NC}"
    exit 1
fi

if [ ! -d "src" ]; then
    echo -e "${RED}❌ 错误: 未找到src目录，项目文件不完整${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 项目文件检查通过${NC}"

# 检查依赖是否安装
echo -e "[4/6] 检查项目依赖..."
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}⚠️  未检测到node_modules目录，正在安装依赖...${NC}"
    echo "   这可能需要几分钟时间，请耐心等待..."
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 错误: 依赖安装失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
else
    echo -e "${GREEN}✅ 依赖已安装${NC}"
fi

# 环境变量检查
echo -e "[5/6] 检查环境配置..."
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  未找到.env文件，使用默认配置${NC}"
else
    echo -e "${GREEN}✅ 环境配置文件存在${NC}"
fi

# 启动开发服务器
echo -e "[6/6] 启动开发服务器..."
echo ""
echo -e "${BLUE}🚀 正在启动露天矿山道路设计软件...${NC}"
echo "   - 服务器地址: http://localhost:3000"
echo "   - 按 Ctrl+C 可停止服务器"
echo ""

# 设置环境变量
export NODE_ENV=development
export VITE_DEBUG_MODE=true

# 启动服务器
npm run dev

# 如果服务器异常退出
echo ""
echo -e "${YELLOW}⚠️  开发服务器已停止${NC}"
