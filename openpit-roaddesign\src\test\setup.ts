/**
 * 测试环境设置
 */

import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// 模拟 Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn(),
    alert: vi.fn(),
    prompt: vi.fn()
  },
  ElNotification: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// 模拟 Cesium
vi.mock('cesium', () => ({
  Viewer: vi.fn(() => ({
    scene: {
      globe: {
        enableLighting: false
      },
      primitives: {
        add: vi.fn(),
        remove: vi.fn()
      }
    },
    entities: {
      add: vi.fn(),
      remove: vi.fn(),
      removeAll: vi.fn()
    },
    camera: {
      flyTo: vi.fn(),
      setView: vi.fn()
    },
    destroy: vi.fn()
  })),
  Cartesian3: {
    fromDegrees: vi.fn(() => ({ x: 0, y: 0, z: 0 }))
  },
  Color: {
    YELLOW: { r: 1, g: 1, b: 0, a: 1 },
    RED: { r: 1, g: 0, b: 0, a: 1 },
    BLUE: { r: 0, g: 0, b: 1, a: 1 }
  },
  Entity: vi.fn(),
  PointGraphics: vi.fn(),
  LabelGraphics: vi.fn()
}))

// 模拟浏览器 API
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// 模拟 localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// 模拟 URL.createObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  writable: true,
  value: vi.fn(() => 'mocked-url')
})

Object.defineProperty(URL, 'revokeObjectURL', {
  writable: true,
  value: vi.fn()
})

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// 全局测试配置
config.global.stubs = {
  'el-icon': true,
  'el-button': true,
  'el-card': true,
  'el-table': true,
  'el-form': true,
  'el-input': true,
  'el-select': true,
  'el-option': true,
  'el-checkbox': true,
  'el-radio': true,
  'el-switch': true,
  'el-tabs': true,
  'el-tab-pane': true,
  'el-dialog': true,
  'el-drawer': true,
  'el-tooltip': true,
  'el-popover': true,
  'el-dropdown': true,
  'el-menu': true,
  'el-menu-item': true,
  'el-progress': true,
  'el-tag': true,
  'el-badge': true,
  'el-alert': true,
  'el-loading': true,
  'el-pagination': true,
  'el-upload': true,
  'el-tree': true,
  'el-collapse': true,
  'el-collapse-item': true,
  'el-statistic': true,
  'el-input-number': true,
  'el-checkbox-group': true,
  'el-radio-group': true,
  'el-button-group': true,
  'el-row': true,
  'el-col': true,
  'el-divider': true,
  'el-empty': true
}

// 设置全局测试超时
vi.setConfig({
  testTimeout: 10000,
  hookTimeout: 10000
})
