<script setup lang="ts">
import { onMounted } from 'vue'
import { useAppStore } from '@/stores/counter'
import { useGISStore } from '@/stores/gis'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppSidebar from '@/components/layout/AppSidebar.vue'

const appStore = useAppStore()
const gisStore = useGISStore()

onMounted(() => {
  // 设置默认主题
  appStore.setTheme('dark')

  // 初始化示例数据
  gisStore.initSampleData()
})
</script>

<template>
  <div id="app" class="app-container">
    <!-- 标题栏 -->
    <AppHeader />

    <!-- 主体内容 -->
    <div class="app-body">
      <!-- 左侧边栏 -->
      <AppSidebar
        :collapsed="appStore.uiState.sidebarCollapsed"
        @toggle="appStore.toggleSidebar"
      />

      <!-- 主内容区 - 使用路由视图 -->
      <main class="app-main">
        <div class="main-content">
          <RouterView />
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
  color: var(--color-text);
}

.app-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
  overflow: hidden;
}

.main-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
  background-color: var(--color-background);
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 8px;
}

.main-content::-webkit-scrollbar-track {
  background: var(--color-background-soft);
}

.main-content::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}
</style>
