<template>
  <div class="data-create-dialog">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <!-- 基础信息 -->
      <el-form-item label="数据名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入数据名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="坐标系统" prop="coordinateSystem">
        <el-select v-model="form.coordinateSystem" style="width: 100%">
          <el-option
            v-for="cs in coordinateSystems"
            :key="cs.code"
            :label="cs.name"
            :value="cs.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="描述信息">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="可选的描述信息"
        />
      </el-form-item>

      <!-- 地形数据特定字段 -->
      <template v-if="dataType === 'terrain'">
        <el-form-item label="分辨率(m)" prop="resolution">
          <el-input-number
            v-model="form.resolution"
            :min="0.1"
            :max="100"
            :step="0.1"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="高程范围">
          <div class="elevation-range">
            <el-input-number
              v-model="form.elevationMin"
              placeholder="最小高程"
              controls-position="right"
            />
            <span>至</span>
            <el-input-number
              v-model="form.elevationMax"
              placeholder="最大高程"
              controls-position="right"
            />
          </div>
        </el-form-item>
      </template>

      <!-- 钻孔数据特定字段 -->
      <template v-if="dataType === 'drilling'">
        <el-form-item label="钻孔位置">
          <div class="coordinate-inputs">
            <el-input-number
              v-model="form.longitude"
              placeholder="经度"
              :precision="6"
              controls-position="right"
            />
            <el-input-number
              v-model="form.latitude"
              placeholder="纬度"
              :precision="6"
              controls-position="right"
            />
            <el-input-number
              v-model="form.elevation"
              placeholder="高程"
              :precision="2"
              controls-position="right"
            />
          </div>
        </el-form-item>
        <el-form-item label="钻孔深度(m)" prop="depth">
          <el-input-number
            v-model="form.depth"
            :min="1"
            :max="1000"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="钻孔直径(mm)">
          <el-input-number
            v-model="form.diameter"
            :min="50"
            :max="500"
            controls-position="right"
          />
        </el-form-item>
      </template>

      <!-- 地质数据特定字段 -->
      <template v-if="dataType === 'geology'">
        <el-form-item label="地质年代">
          <el-select v-model="form.geologicalAge" style="width: 100%">
            <el-option label="第四纪" value="quaternary" />
            <el-option label="新近纪" value="neogene" />
            <el-option label="古近纪" value="paleogene" />
            <el-option label="白垩纪" value="cretaceous" />
            <el-option label="侏罗纪" value="jurassic" />
            <el-option label="三叠纪" value="triassic" />
          </el-select>
        </el-form-item>
        <el-form-item label="岩石类型">
          <el-select v-model="form.rockType" style="width: 100%">
            <el-option label="花岗岩" value="granite" />
            <el-option label="石灰岩" value="limestone" />
            <el-option label="砂岩" value="sandstone" />
            <el-option label="页岩" value="shale" />
            <el-option label="玄武岩" value="basalt" />
          </el-select>
        </el-form-item>
        <el-form-item label="显示颜色">
          <el-color-picker v-model="form.color" />
        </el-form-item>
      </template>

      <!-- CAD数据特定字段 -->
      <template v-if="dataType === 'cad'">
        <el-form-item label="CAD格式">
          <el-select v-model="form.cadFormat" style="width: 100%">
            <el-option label="DWG" value="dwg" />
            <el-option label="DXF" value="dxf" />
            <el-option label="STEP" value="step" />
          </el-select>
        </el-form-item>
        <el-form-item label="单位系统">
          <el-select v-model="form.units" style="width: 100%">
            <el-option label="米" value="meter" />
            <el-option label="毫米" value="millimeter" />
            <el-option label="英尺" value="foot" />
          </el-select>
        </el-form-item>
      </template>

      <!-- 道路数据特定字段 -->
      <template v-if="dataType === 'road'">
        <el-form-item label="道路类型">
          <el-select v-model="form.roadType" style="width: 100%">
            <el-option label="主要道路" value="main" />
            <el-option label="分支道路" value="branch" />
            <el-option label="通道道路" value="access" />
            <el-option label="运输道路" value="haul" />
          </el-select>
        </el-form-item>
        <el-form-item label="道路宽度(m)">
          <el-input-number
            v-model="form.width"
            :min="3"
            :max="20"
            :step="0.5"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item label="设计速度(km/h)">
          <el-input-number
            v-model="form.designSpeed"
            :min="10"
            :max="80"
            :step="5"
            controls-position="right"
          />
        </el-form-item>
      </template>

      <!-- 可见性设置 -->
      <el-form-item label="初始状态">
        <div class="visibility-settings">
          <el-switch
            v-model="form.visible"
            active-text="可见"
            inactive-text="隐藏"
          />
          <el-slider
            v-model="form.opacity"
            :min="0"
            :max="100"
            style="width: 150px; margin-left: 20px;"
          />
          <span class="opacity-label">透明度: {{ form.opacity }}%</span>
        </div>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="dialog-actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button
        type="primary"
        :loading="creating"
        @click="handleCreate"
      >
        {{ creating ? '创建中...' : '创建' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { DEFAULT_COORDINATE_SYSTEMS } from '@/config'
import type { GISData } from '@/types'

interface Props {
  dataType: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'create-success': [data: GISData]
  'cancel': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const creating = ref(false)

// 表单数据
const form = reactive({
  name: '',
  coordinateSystem: 'EPSG:4326',
  description: '',
  visible: true,
  opacity: 80,
  
  // 地形数据字段
  resolution: 1.0,
  elevationMin: 0,
  elevationMax: 1000,
  
  // 钻孔数据字段
  longitude: 116.3974,
  latitude: 39.9093,
  elevation: 50,
  depth: 100,
  diameter: 150,
  
  // 地质数据字段
  geologicalAge: 'quaternary',
  rockType: 'granite',
  color: '#ff6b6b',
  
  // CAD数据字段
  cadFormat: 'dwg',
  units: 'meter',
  
  // 道路数据字段
  roadType: 'main',
  width: 7,
  designSpeed: 40
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入数据名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度应在2-50个字符', trigger: 'blur' }
  ],
  coordinateSystem: [
    { required: true, message: '请选择坐标系统', trigger: 'change' }
  ],
  resolution: [
    { required: true, message: '请输入分辨率', trigger: 'blur' }
  ],
  depth: [
    { required: true, message: '请输入钻孔深度', trigger: 'blur' }
  ]
}

// 计算属性
const coordinateSystems = computed(() => DEFAULT_COORDINATE_SYSTEMS)

// 方法
async function handleCreate() {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    creating.value = true
    
    // 模拟创建过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const newData: GISData = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      name: form.name,
      type: props.dataType as any,
      size: generateRandomSize(),
      createTime: new Date().toISOString(),
      visible: form.visible,
      opacity: form.opacity,
      coordinateSystem: form.coordinateSystem,
      metadata: generateMetadata()
    }
    
    emit('create-success', newData)
  } catch (error) {
    console.error('创建失败:', error)
    ElMessage.error('表单验证失败，请检查输入')
  } finally {
    creating.value = false
  }
}

function generateRandomSize(): string {
  const size = Math.floor(Math.random() * 50 + 1)
  const unit = Math.random() > 0.5 ? 'MB' : 'KB'
  return `${size}.${Math.floor(Math.random() * 9)}${unit}`
}

function generateMetadata(): Record<string, any> {
  const baseMetadata = {
    description: form.description,
    created: 'manual'
  }
  
  switch (props.dataType) {
    case 'terrain':
      return {
        ...baseMetadata,
        resolution: form.resolution,
        elevation: {
          min: form.elevationMin,
          max: form.elevationMax,
          average: (form.elevationMin + form.elevationMax) / 2
        },
        format: 'tif'
      }
    
    case 'drilling':
      return {
        ...baseMetadata,
        position: {
          longitude: form.longitude,
          latitude: form.latitude,
          elevation: form.elevation
        },
        depth: form.depth,
        diameter: form.diameter,
        angle: 90,
        azimuth: 0,
        samples: []
      }
    
    case 'geology':
      return {
        ...baseMetadata,
        geologicalAge: form.geologicalAge,
        rockType: form.rockType,
        structure: 'unknown',
        mineralComposition: []
      }
    
    case 'cad':
      return {
        ...baseMetadata,
        format: form.cadFormat,
        units: form.units,
        layers: [],
        entities: []
      }
    
    case 'road':
      return {
        ...baseMetadata,
        roadType: form.roadType,
        width: form.width,
        grade: 0,
        designSpeed: form.designSpeed,
        surfaceType: 'asphalt',
        centerline: {
          points: [],
          totalLength: 0,
          horizontalCurves: [],
          verticalCurves: []
        },
        crossSections: []
      }
    
    default:
      return baseMetadata
  }
}
</script>

<style scoped>
.data-create-dialog {
  padding: var(--spacing-md);
}

.elevation-range {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.coordinate-inputs {
  display: flex;
  gap: var(--spacing-sm);
}

.visibility-settings {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.opacity-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
  min-width: 80px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding-top: var(--spacing-lg);
  margin-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

/* Element Plus 样式覆盖 */
:deep(.el-form-item__label) {
  color: var(--color-text);
}

:deep(.el-input__inner) {
  background-color: var(--color-background);
  border-color: var(--color-border);
  color: var(--color-text);
}

:deep(.el-select .el-input__inner) {
  background-color: var(--color-background);
}

:deep(.el-textarea__inner) {
  background-color: var(--color-background);
  border-color: var(--color-border);
  color: var(--color-text);
}
</style>
