<template>
  <div class="safety-check-panel">
    <!-- 检测配置 -->
    <div class="check-config">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Shield /></el-icon>
            <span>安全检测配置</span>
          </div>
        </template>

        <el-form :model="checkConfig" label-width="120px" size="small">
          <el-form-item label="检测道路">
            <el-select v-model="checkConfig.selectedRoad" placeholder="选择要检测的道路">
              <el-option
                v-for="road in availableRoads"
                :key="road.id"
                :label="road.name"
                :value="road.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="检测项目">
            <el-checkbox-group v-model="checkConfig.checkItems">
              <el-checkbox label="grade">坡度检测</el-checkbox>
              <el-checkbox label="radius">转弯半径检测</el-checkbox>
              <el-checkbox label="sight_distance">视距检测</el-checkbox>
              <el-checkbox label="width">道路宽度检测</el-checkbox>
              <el-checkbox label="clearance">净空检测</el-checkbox>
              <el-checkbox label="drainage">排水检测</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="检测标准">
            <el-select v-model="checkConfig.standard">
              <el-option label="露天矿山道路设计规范" value="openpit" />
              <el-option label="公路工程技术标准" value="highway" />
              <el-option label="自定义标准" value="custom" />
            </el-select>
          </el-form-item>

          <el-form-item label="严格程度">
            <el-radio-group v-model="checkConfig.strictness">
              <el-radio label="strict">严格</el-radio>
              <el-radio label="normal">标准</el-radio>
              <el-radio label="loose">宽松</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <div class="action-buttons">
          <el-button type="primary" @click="startSafetyCheck" :loading="checking">
            <el-icon><Search /></el-icon>
            {{ checking ? '检测中...' : '开始检测' }}
          </el-button>
          <el-button @click="clearResults">
            <el-icon><Delete /></el-icon>
            清除结果
          </el-button>
          <el-button @click="exportReport">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 检测结果概览 -->
    <div class="check-overview" v-if="checkResults.length > 0">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>检测结果概览</span>
          </div>
        </template>

        <div class="overview-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="stat-card pass">
                <div class="stat-number">{{ getResultCount('pass') }}</div>
                <div class="stat-label">通过</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card warning">
                <div class="stat-number">{{ getResultCount('warning') }}</div>
                <div class="stat-label">警告</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card fail">
                <div class="stat-number">{{ getResultCount('fail') }}</div>
                <div class="stat-label">不合格</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card total">
                <div class="stat-number">{{ checkResults.length }}</div>
                <div class="stat-label">总计</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="safety-score">
          <div class="score-label">安全评分</div>
          <el-progress
            :percentage="safetyScore"
            :color="getScoreColor(safetyScore)"
            :stroke-width="12"
            text-inside
          />
        </div>
      </el-card>
    </div>

    <!-- 详细检测结果 -->
    <div class="check-results" v-if="checkResults.length > 0">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><List /></el-icon>
            <span>详细检测结果</span>
            <div class="header-actions">
              <el-select v-model="resultFilter" size="small" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="不合格" value="fail" />
                <el-option label="警告" value="warning" />
                <el-option label="通过" value="pass" />
              </el-select>
            </div>
          </div>
        </template>

        <div class="results-table">
          <el-table
            :data="filteredResults"
            stripe
            style="width: 100%"
            @row-click="showResultDetail"
          >
            <el-table-column prop="type" label="检测项目" width="120">
              <template #default="{ row }">
                <el-tag :type="getCheckTypeColor(row.type)">
                  {{ getCheckTypeName(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusColor(row.status)">
                  {{ getStatusName(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="实测值" width="100">
              <template #default="{ row }">
                {{ formatValue(row.value, row.type) }}
              </template>
            </el-table-column>
            <el-table-column prop="standard" label="标准值" width="100">
              <template #default="{ row }">
                {{ formatValue(row.standard, row.type) }}
              </template>
            </el-table-column>
            <el-table-column prop="message" label="描述" min-width="200" />
            <el-table-column prop="location" label="位置" width="120">
              <template #default="{ row }">
                <span v-if="row.location">
                  K{{ row.location.station.toFixed(0) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" @click.stop="locateIssue(row)">
                  <el-icon><Location /></el-icon>
                  定位
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 安全建议 -->
    <div class="safety-suggestions" v-if="suggestions.length > 0">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Warning /></el-icon>
            <span>安全建议</span>
          </div>
        </template>

        <div class="suggestions-list">
          <div
            v-for="(suggestion, index) in suggestions"
            :key="index"
            class="suggestion-item"
            :class="suggestion.priority"
          >
            <div class="suggestion-header">
              <el-icon>
                <Warning v-if="suggestion.priority === 'high'" />
                <InfoFilled v-else-if="suggestion.priority === 'medium'" />
                <QuestionFilled v-else />
              </el-icon>
              <span class="suggestion-title">{{ suggestion.title }}</span>
              <el-tag :type="getPriorityColor(suggestion.priority)" size="small">
                {{ getPriorityName(suggestion.priority) }}
              </el-tag>
            </div>
            <div class="suggestion-content">
              {{ suggestion.content }}
            </div>
            <div class="suggestion-actions" v-if="suggestion.actions">
              <el-button
                v-for="action in suggestion.actions"
                :key="action.label"
                size="small"
                @click="executeSuggestionAction(action)"
              >
                {{ action.label }}
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { 
  Shield, 
  Search, 
  Delete, 
  Download, 
  DataAnalysis, 
  List, 
  Location, 
  Warning, 
  InfoFilled, 
  QuestionFilled 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { SafetyCheckResult } from '@/types'

// 响应式数据
const checking = ref(false)
const resultFilter = ref('all')

// 检测配置
const checkConfig = reactive({
  selectedRoad: '',
  checkItems: ['grade', 'radius', 'sight_distance', 'width'],
  standard: 'openpit',
  strictness: 'normal'
})

// 可用道路
const availableRoads = ref([
  { id: '1', name: '主干道A' },
  { id: '2', name: '分支道路B' },
  { id: '3', name: '运输道路C' }
])

// 检测结果
const checkResults = ref<SafetyCheckResult[]>([
  {
    type: 'grade',
    status: 'fail',
    value: 12.5,
    standard: 8,
    message: '纵坡过大，超出设计标准',
    location: { station: 125, coordinates: [116.3974, 39.9093, 50] }
  },
  {
    type: 'radius',
    status: 'warning',
    value: 45,
    standard: 50,
    message: '转弯半径略小于标准值',
    location: { station: 280, coordinates: [116.3984, 39.9103, 55] }
  },
  {
    type: 'sight_distance',
    status: 'pass',
    value: 120,
    standard: 100,
    message: '视距满足要求',
    location: { station: 350, coordinates: [116.3994, 39.9113, 60] }
  }
])

// 安全建议
const suggestions = ref([
  {
    priority: 'high',
    title: '纵坡过大问题',
    content: '建议在K125处增设缓坡段，或考虑调整线形以降低纵坡。',
    actions: [
      { label: '查看详情', action: 'view_detail' },
      { label: '生成方案', action: 'generate_solution' }
    ]
  },
  {
    priority: 'medium',
    title: '转弯半径优化',
    content: '建议在K280处适当增大转弯半径，提高行车安全性。',
    actions: [
      { label: '优化设计', action: 'optimize_design' }
    ]
  }
])

// 计算属性
const filteredResults = computed(() => {
  if (resultFilter.value === 'all') {
    return checkResults.value
  }
  return checkResults.value.filter(result => result.status === resultFilter.value)
})

const safetyScore = computed(() => {
  if (checkResults.value.length === 0) return 100
  
  const passCount = getResultCount('pass')
  const warningCount = getResultCount('warning')
  const failCount = getResultCount('fail')
  
  // 计算安全评分
  const score = (passCount * 100 + warningCount * 70 + failCount * 0) / checkResults.value.length
  return Math.round(score)
})

// 方法
async function startSafetyCheck() {
  if (!checkConfig.selectedRoad) {
    ElMessage.warning('请选择要检测的道路')
    return
  }

  checking.value = true
  
  try {
    // 模拟安全检测过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    ElMessage.success('安全检测完成')
  } catch (error) {
    console.error('安全检测失败:', error)
    ElMessage.error('安全检测失败')
  } finally {
    checking.value = false
  }
}

function clearResults() {
  checkResults.value = []
  suggestions.value = []
  ElMessage.success('已清除检测结果')
}

function exportReport() {
  ElMessage.success('安全检测报告导出成功')
}

function getResultCount(status: string): number {
  return checkResults.value.filter(result => result.status === status).length
}

function getScoreColor(score: number): string {
  if (score >= 90) return '#67c23a'
  if (score >= 70) return '#e6a23c'
  return '#f56c6c'
}

function getCheckTypeColor(type: string): string {
  const colors = {
    grade: 'primary',
    radius: 'success',
    sight_distance: 'info',
    width: 'warning'
  }
  return colors[type as keyof typeof colors] || 'default'
}

function getCheckTypeName(type: string): string {
  const names = {
    grade: '坡度',
    radius: '半径',
    sight_distance: '视距',
    width: '宽度'
  }
  return names[type as keyof typeof names] || type
}

function getStatusColor(status: string): string {
  const colors = {
    pass: 'success',
    warning: 'warning',
    fail: 'danger'
  }
  return colors[status as keyof typeof colors] || 'info'
}

function getStatusName(status: string): string {
  const names = {
    pass: '通过',
    warning: '警告',
    fail: '不合格'
  }
  return names[status as keyof typeof names] || status
}

function formatValue(value: number, type: string): string {
  const units = {
    grade: '%',
    radius: 'm',
    sight_distance: 'm',
    width: 'm'
  }
  const unit = units[type as keyof typeof units] || ''
  return `${value.toFixed(1)}${unit}`
}

function showResultDetail(row: SafetyCheckResult) {
  ElMessage.info(`查看详情: ${row.message}`)
}

function locateIssue(result: SafetyCheckResult) {
  if (result.location) {
    ElMessage.success(`已定位到桩号 K${result.location.station.toFixed(0)}`)
  }
}

function getPriorityColor(priority: string): string {
  const colors = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return colors[priority as keyof typeof colors] || 'info'
}

function getPriorityName(priority: string): string {
  const names = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return names[priority as keyof typeof names] || priority
}

function executeSuggestionAction(action: any) {
  ElMessage.info(`执行操作: ${action.label}`)
}
</script>

<style scoped>
.safety-check-panel {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  height: 100%;
  overflow: auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.header-actions {
  margin-left: auto;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

.overview-stats {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  text-align: center;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-base);
  border: 2px solid;
}

.stat-card.pass {
  border-color: var(--color-success);
  background-color: rgba(103, 194, 58, 0.1);
}

.stat-card.warning {
  border-color: var(--color-warning);
  background-color: rgba(230, 162, 60, 0.1);
}

.stat-card.fail {
  border-color: var(--color-danger);
  background-color: rgba(245, 108, 108, 0.1);
}

.stat-card.total {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: bold;
  color: var(--color-text);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
  margin-top: var(--spacing-xs);
}

.safety-score {
  text-align: center;
}

.score-label {
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--color-text);
  margin-bottom: var(--spacing-md);
}

.results-table {
  margin-top: var(--spacing-md);
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.suggestion-item {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-base);
  border-left: 4px solid;
}

.suggestion-item.high {
  border-left-color: var(--color-danger);
  background-color: rgba(245, 108, 108, 0.05);
}

.suggestion-item.medium {
  border-left-color: var(--color-warning);
  background-color: rgba(230, 162, 60, 0.05);
}

.suggestion-item.low {
  border-left-color: var(--color-info);
  background-color: rgba(144, 147, 153, 0.05);
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.suggestion-title {
  flex: 1;
  font-weight: bold;
  color: var(--color-text);
}

.suggestion-content {
  color: var(--color-text-soft);
  line-height: 1.5;
  margin-bottom: var(--spacing-sm);
}

.suggestion-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* Element Plus 样式覆盖 */
:deep(.el-progress-bar__outer) {
  background-color: var(--color-background-soft);
}

:deep(.el-table__row:hover) {
  background-color: var(--color-background-soft);
  cursor: pointer;
}
</style>
