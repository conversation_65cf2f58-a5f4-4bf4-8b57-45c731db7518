<template>
  <div class="safety-check-panel">
    <!-- 检测配置 -->
    <div class="check-config">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Shield /></el-icon>
            <span>安全检测配置</span>
          </div>
        </template>

        <el-form :model="checkConfig" label-width="120px" size="small">
          <el-form-item label="检测道路">
            <el-select v-model="checkConfig.selectedRoad" placeholder="选择要检测的道路">
              <el-option
                v-for="road in availableRoads"
                :key="road.id"
                :label="road.name"
                :value="road.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="检测项目">
            <el-checkbox-group v-model="checkConfig.checkItems">
              <el-checkbox label="grade">坡度检测</el-checkbox>
              <el-checkbox label="radius">转弯半径检测</el-checkbox>
              <el-checkbox label="sight_distance">视距检测</el-checkbox>
              <el-checkbox label="width">道路宽度检测</el-checkbox>
              <el-checkbox label="clearance">净空检测</el-checkbox>
              <el-checkbox label="drainage">排水检测</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="检测标准">
            <el-select v-model="checkConfig.standard">
              <el-option label="露天矿山道路设计规范" value="openpit" />
              <el-option label="公路工程技术标准" value="highway" />
              <el-option label="自定义标准" value="custom" />
            </el-select>
          </el-form-item>

          <el-form-item label="严格程度">
            <el-radio-group v-model="checkConfig.strictness">
              <el-radio label="strict">严格</el-radio>
              <el-radio label="normal">标准</el-radio>
              <el-radio label="loose">宽松</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

        <div class="action-buttons">
          <el-button type="primary" @click="startSafetyCheck" :loading="checking">
            <el-icon><Search /></el-icon>
            {{ checking ? '检测中...' : '开始检测' }}
          </el-button>
          <el-button @click="clearResults">
            <el-icon><Delete /></el-icon>
            清除结果
          </el-button>
          <el-button @click="exportReport">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 检测结果概览 -->
    <div class="check-overview" v-if="checkResults.length > 0">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>检测结果概览</span>
          </div>
        </template>

        <div class="overview-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="stat-card pass">
                <div class="stat-number">{{ getResultCount('pass') }}</div>
                <div class="stat-label">通过</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card warning">
                <div class="stat-number">{{ getResultCount('warning') }}</div>
                <div class="stat-label">警告</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card fail">
                <div class="stat-number">{{ getResultCount('fail') }}</div>
                <div class="stat-label">不合格</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-card total">
                <div class="stat-number">{{ checkResults.length }}</div>
                <div class="stat-label">总计</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="safety-score">
          <div class="score-label">安全评分</div>
          <el-progress
            :percentage="safetyScore"
            :color="getScoreColor(safetyScore)"
            :stroke-width="12"
            text-inside
          />
        </div>
      </el-card>
    </div>

    <!-- 详细检测结果 -->
    <div class="check-results" v-if="checkResults.length > 0">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><List /></el-icon>
            <span>详细检测结果</span>
            <div class="header-actions">
              <el-select v-model="resultFilter" size="small" style="width: 120px">
                <el-option label="全部" value="all" />
                <el-option label="不合格" value="fail" />
                <el-option label="警告" value="warning" />
                <el-option label="通过" value="pass" />
              </el-select>
            </div>
          </div>
        </template>

        <div class="results-table">
          <el-table
            :data="filteredResults"
            stripe
            style="width: 100%"
            @row-click="showResultDetail"
          >
            <el-table-column prop="type" label="检测项目" width="120">
              <template #default="{ row }">
                <el-tag :type="getCheckTypeColor(row.type)">
                  {{ getCheckTypeName(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusColor(row.status)">
                  {{ getStatusName(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="实测值" width="100">
              <template #default="{ row }">
                {{ formatValue(row.value, row.type) }}
              </template>
            </el-table-column>
            <el-table-column prop="standard" label="标准值" width="100">
              <template #default="{ row }">
                {{ formatValue(row.standard, row.type) }}
              </template>
            </el-table-column>
            <el-table-column prop="message" label="描述" min-width="200" />
            <el-table-column prop="location" label="位置" width="120">
              <template #default="{ row }">
                <span v-if="row.location">
                  K{{ row.location.station.toFixed(0) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" @click.stop="locateIssue(row)">
                  <el-icon><Location /></el-icon>
                  定位
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 安全建议 -->
    <div class="safety-suggestions" v-if="suggestions.length > 0">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Warning /></el-icon>
            <span>安全建议</span>
          </div>
        </template>

        <div class="suggestions-list">
          <div
            v-for="(suggestion, index) in suggestions"
            :key="index"
            class="suggestion-item"
            :class="suggestion.priority"
          >
            <div class="suggestion-header">
              <el-icon>
                <Warning v-if="suggestion.priority === 'high'" />
                <InfoFilled v-else-if="suggestion.priority === 'medium'" />
                <QuestionFilled v-else />
              </el-icon>
              <span class="suggestion-title">{{ suggestion.title }}</span>
              <el-tag :type="getPriorityColor(suggestion.priority)" size="small">
                {{ getPriorityName(suggestion.priority) }}
              </el-tag>
            </div>
            <div class="suggestion-content">
              {{ suggestion.content }}
            </div>
            <div class="suggestion-actions" v-if="suggestion.actions">
              <el-button
                v-for="action in suggestion.actions"
                :key="action.label"
                size="small"
                @click="executeSuggestionAction(action)"
              >
                {{ action.label }}
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import {
  Shield,
  Search,
  Delete,
  Download,
  DataAnalysis,
  List,
  Location,
  Warning,
  InfoFilled,
  QuestionFilled
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { RoadSafetyChecker, SafetyReportGenerator } from '@/utils/safetyCheckAlgorithms'
import { useGISStore } from '@/stores/gis'
import type { SafetyCheckResult, RoadData } from '@/types'

const gisStore = useGISStore()

// 响应式数据
const checking = ref(false)
const resultFilter = ref('all')

// 检测配置
const checkConfig = reactive({
  selectedRoad: '',
  checkItems: ['grade', 'radius', 'sight_distance', 'width', 'clearance', 'drainage'],
  standard: 'openpit',
  strictness: 'normal'
})

// 可用道路 - 从GIS存储中获取道路数据
const availableRoads = computed(() => {
  return gisStore.allData
    .filter(data => data.type === 'road')
    .map(road => ({ id: road.id, name: road.name }))
})

// 检测结果
const checkResults = ref<SafetyCheckResult[]>([])

// 安全检测器实例
let safetyChecker: RoadSafetyChecker | null = null

// 安全建议
const suggestions = ref([
  {
    priority: 'high',
    title: '纵坡过大问题',
    content: '建议在K125处增设缓坡段，或考虑调整线形以降低纵坡。',
    actions: [
      { label: '查看详情', action: 'view_detail' },
      { label: '生成方案', action: 'generate_solution' }
    ]
  },
  {
    priority: 'medium',
    title: '转弯半径优化',
    content: '建议在K280处适当增大转弯半径，提高行车安全性。',
    actions: [
      { label: '优化设计', action: 'optimize_design' }
    ]
  }
])

// 计算属性
const filteredResults = computed(() => {
  if (resultFilter.value === 'all') {
    return checkResults.value
  }
  return checkResults.value.filter(result => result.status === resultFilter.value)
})

const safetyScore = computed(() => {
  if (checkResults.value.length === 0) return 100
  
  const passCount = getResultCount('pass')
  const warningCount = getResultCount('warning')
  const failCount = getResultCount('fail')
  
  // 计算安全评分
  const score = (passCount * 100 + warningCount * 70 + failCount * 0) / checkResults.value.length
  return Math.round(score)
})

// 方法
async function startSafetyCheck() {
  if (!checkConfig.selectedRoad) {
    ElMessage.warning('请选择要检测的道路')
    return
  }

  // 获取选中的道路数据
  const roadGISData = gisStore.allData.find(data => data.id === checkConfig.selectedRoad)
  if (!roadGISData) {
    ElMessage.error('未找到选中的道路数据')
    return
  }

  checking.value = true

  try {
    // 创建安全检测器
    safetyChecker = new RoadSafetyChecker({
      roadType: getRoadTypeFromGISData(roadGISData),
      strictness: checkConfig.strictness as any,
      checkItems: checkConfig.checkItems
    })

    // 转换GIS数据为道路数据格式
    const roadData = convertGISDataToRoadData(roadGISData)

    // 执行安全检测
    const results = await safetyChecker.performSafetyCheck(roadData)
    checkResults.value = results

    // 生成安全建议
    updateSafetyRecommendations(results)

    ElMessage.success(`安全检测完成，发现 ${results.length} 个检测项目`)
  } catch (error) {
    console.error('安全检测失败:', error)
    ElMessage.error('安全检测失败: ' + (error as Error).message)
  } finally {
    checking.value = false
  }
}

function clearResults() {
  checkResults.value = []
  suggestions.value = []
  ElMessage.success('已清除检测结果')
}

function exportReport() {
  if (checkResults.value.length === 0) {
    ElMessage.warning('没有检测结果可以导出')
    return
  }

  try {
    // 获取道路数据
    const roadGISData = gisStore.allData.find(data => data.id === checkConfig.selectedRoad)
    if (!roadGISData || !safetyChecker) {
      ElMessage.error('无法生成报告：缺少必要数据')
      return
    }

    const roadData = convertGISDataToRoadData(roadGISData)
    const summary = safetyChecker.generateSummary(checkResults.value)
    const reportContent = SafetyReportGenerator.generateReport(roadData, checkResults.value, summary)

    // 创建并下载报告文件
    const blob = new Blob([reportContent], { type: 'text/markdown;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${roadData.name}_安全检测报告_${new Date().toISOString().split('T')[0]}.md`
    link.click()
    URL.revokeObjectURL(url)

    ElMessage.success('安全检测报告导出成功')
  } catch (error) {
    console.error('报告导出失败:', error)
    ElMessage.error('报告导出失败')
  }
}

function getResultCount(status: string): number {
  return checkResults.value.filter(result => result.status === status).length
}

function getScoreColor(score: number): string {
  if (score >= 90) return '#67c23a'
  if (score >= 70) return '#e6a23c'
  return '#f56c6c'
}

function getCheckTypeColor(type: string): string {
  const colors = {
    grade: 'primary',
    radius: 'success',
    sight_distance: 'info',
    width: 'warning'
  }
  return colors[type as keyof typeof colors] || 'default'
}

function getCheckTypeName(type: string): string {
  const names = {
    grade: '坡度',
    radius: '半径',
    sight_distance: '视距',
    width: '宽度'
  }
  return names[type as keyof typeof names] || type
}

function getStatusColor(status: string): string {
  const colors = {
    pass: 'success',
    warning: 'warning',
    fail: 'danger'
  }
  return colors[status as keyof typeof colors] || 'info'
}

function getStatusName(status: string): string {
  const names = {
    pass: '通过',
    warning: '警告',
    fail: '不合格'
  }
  return names[status as keyof typeof names] || status
}

function formatValue(value: number, type: string): string {
  const units = {
    grade: '%',
    radius: 'm',
    sight_distance: 'm',
    width: 'm'
  }
  const unit = units[type as keyof typeof units] || ''
  return `${value.toFixed(1)}${unit}`
}

function showResultDetail(row: SafetyCheckResult) {
  ElMessage.info(`查看详情: ${row.message}`)
}

function locateIssue(result: SafetyCheckResult) {
  if (result.location) {
    ElMessage.success(`已定位到桩号 K${result.location.station.toFixed(0)}`)
  }
}

function getPriorityColor(priority: string): string {
  const colors = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return colors[priority as keyof typeof colors] || 'info'
}

function getPriorityName(priority: string): string {
  const names = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return names[priority as keyof typeof names] || priority
}

function executeSuggestionAction(action: any) {
  ElMessage.info(`执行操作: ${action.label}`)
}

// 辅助函数
function getRoadTypeFromGISData(gisData: any): string {
  // 从GIS数据的元数据中获取道路类型
  return gisData.metadata?.roadType || 'main'
}

function convertGISDataToRoadData(gisData: any): RoadData {
  // 将GIS数据转换为道路数据格式
  return {
    id: gisData.id,
    name: gisData.name,
    type: 'road',
    centerline: {
      points: generateMockCenterlinePoints(), // 生成模拟的中心线点
      totalLength: 1200,
      horizontalCurves: generateMockHorizontalCurves(),
      verticalCurves: []
    },
    metadata: {
      width: 7,
      roadType: gisData.metadata?.roadType || 'main',
      designSpeed: 40,
      ...gisData.metadata
    }
  } as RoadData
}

function generateMockCenterlinePoints() {
  // 生成模拟的中心线点数据
  const points = []
  for (let i = 0; i <= 120; i++) {
    points.push({
      longitude: 116.3974 + i * 0.0001,
      latitude: 39.9093 + i * 0.0001 + Math.sin(i * 0.1) * 0.00005,
      elevation: 50 + i * 0.5 + Math.sin(i * 0.2) * 10,
      station: i * 10
    })
  }
  return points
}

function generateMockHorizontalCurves() {
  // 生成模拟的水平曲线数据
  return [
    {
      station: 200,
      radius: 60,
      length: 80,
      direction: 'left' as const
    },
    {
      station: 600,
      radius: 45,
      length: 60,
      direction: 'right' as const
    }
  ]
}

function updateSafetyRecommendations(results: SafetyCheckResult[]) {
  // 根据检测结果更新安全建议
  const newSuggestions = []

  const failResults = results.filter(r => r.status === 'fail')
  const warningResults = results.filter(r => r.status === 'warning')

  // 生成高优先级建议
  failResults.forEach(result => {
    newSuggestions.push({
      priority: 'high',
      title: `${getCheckTypeName(result.type)}不合格`,
      content: `${result.message}。建议立即调整设计参数以满足安全要求。`,
      actions: [
        { label: '查看详情', action: 'view_detail' },
        { label: '调整设计', action: 'adjust_design' }
      ]
    })
  })

  // 生成中优先级建议
  warningResults.forEach(result => {
    newSuggestions.push({
      priority: 'medium',
      title: `${getCheckTypeName(result.type)}需要注意`,
      content: `${result.message}。建议优化设计以提高安全性。`,
      actions: [
        { label: '优化设计', action: 'optimize_design' }
      ]
    })
  })

  suggestions.value = newSuggestions
}
</script>

<style scoped>
.safety-check-panel {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  height: 100%;
  overflow: auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.header-actions {
  margin-left: auto;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

.overview-stats {
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  text-align: center;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-base);
  border: 2px solid;
}

.stat-card.pass {
  border-color: var(--color-success);
  background-color: rgba(103, 194, 58, 0.1);
}

.stat-card.warning {
  border-color: var(--color-warning);
  background-color: rgba(230, 162, 60, 0.1);
}

.stat-card.fail {
  border-color: var(--color-danger);
  background-color: rgba(245, 108, 108, 0.1);
}

.stat-card.total {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: bold;
  color: var(--color-text);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
  margin-top: var(--spacing-xs);
}

.safety-score {
  text-align: center;
}

.score-label {
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--color-text);
  margin-bottom: var(--spacing-md);
}

.results-table {
  margin-top: var(--spacing-md);
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.suggestion-item {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-base);
  border-left: 4px solid;
}

.suggestion-item.high {
  border-left-color: var(--color-danger);
  background-color: rgba(245, 108, 108, 0.05);
}

.suggestion-item.medium {
  border-left-color: var(--color-warning);
  background-color: rgba(230, 162, 60, 0.05);
}

.suggestion-item.low {
  border-left-color: var(--color-info);
  background-color: rgba(144, 147, 153, 0.05);
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.suggestion-title {
  flex: 1;
  font-weight: bold;
  color: var(--color-text);
}

.suggestion-content {
  color: var(--color-text-soft);
  line-height: 1.5;
  margin-bottom: var(--spacing-sm);
}

.suggestion-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* Element Plus 样式覆盖 */
:deep(.el-progress-bar__outer) {
  background-color: var(--color-background-soft);
}

:deep(.el-table__row:hover) {
  background-color: var(--color-background-soft);
  cursor: pointer;
}
</style>
