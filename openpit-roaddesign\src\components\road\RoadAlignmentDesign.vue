<template>
  <div class="road-alignment-design">
    <!-- 选线参数设置 -->
    <div class="alignment-params">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Route /></el-icon>
            <span>道路选线参数</span>
          </div>
        </template>
        
        <el-form :model="alignmentParams" label-width="100px" size="small">
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="起点坐标">
                <div class="coordinate-input">
                  <el-input-number
                    v-model="alignmentParams.startPoint.longitude"
                    :precision="6"
                    placeholder="经度"
                    controls-position="right"
                  />
                  <el-input-number
                    v-model="alignmentParams.startPoint.latitude"
                    :precision="6"
                    placeholder="纬度"
                    controls-position="right"
                  />
                  <el-input-number
                    v-model="alignmentParams.startPoint.elevation"
                    :precision="2"
                    placeholder="高程"
                    controls-position="right"
                  />
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="终点坐标">
                <div class="coordinate-input">
                  <el-input-number
                    v-model="alignmentParams.endPoint.longitude"
                    :precision="6"
                    placeholder="经度"
                    controls-position="right"
                  />
                  <el-input-number
                    v-model="alignmentParams.endPoint.latitude"
                    :precision="6"
                    placeholder="纬度"
                    controls-position="right"
                  />
                  <el-input-number
                    v-model="alignmentParams.endPoint.elevation"
                    :precision="2"
                    placeholder="高程"
                    controls-position="right"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="选线方法">
            <el-radio-group v-model="alignmentParams.method">
              <el-radio label="auto">自动选线</el-radio>
              <el-radio label="manual">手动选线</el-radio>
              <el-radio label="template">模板选线</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="优化目标">
            <el-checkbox-group v-model="alignmentParams.objectives">
              <el-checkbox label="length">最短路径</el-checkbox>
              <el-checkbox label="earthwork">最小土方</el-checkbox>
              <el-checkbox label="grade">平缓坡度</el-checkbox>
              <el-checkbox label="curve">减少弯道</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="约束条件">
            <div class="constraints-grid">
              <div class="constraint-item">
                <span>最大坡度:</span>
                <el-input-number
                  v-model="alignmentParams.constraints.maxGrade"
                  :min="1"
                  :max="15"
                  :step="0.5"
                  size="small"
                />
                <span>%</span>
              </div>
              <div class="constraint-item">
                <span>最小半径:</span>
                <el-input-number
                  v-model="alignmentParams.constraints.minRadius"
                  :min="10"
                  :max="200"
                  :step="5"
                  size="small"
                />
                <span>m</span>
              </div>
              <div class="constraint-item">
                <span>最小视距:</span>
                <el-input-number
                  v-model="alignmentParams.constraints.minSightDistance"
                  :min="30"
                  :max="300"
                  :step="10"
                  size="small"
                />
                <span>m</span>
              </div>
            </div>
          </el-form-item>
        </el-form>

        <div class="action-buttons">
          <el-button type="primary" @click="generateAlignment" :loading="generating">
            <el-icon><MagicStick /></el-icon>
            {{ generating ? '生成中...' : '生成选线' }}
          </el-button>
          <el-button @click="clearAlignment">
            <el-icon><Delete /></el-icon>
            清除
          </el-button>
          <el-button @click="importWaypoints">
            <el-icon><Upload /></el-icon>
            导入控制点
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 控制点管理 -->
    <div class="waypoints-management" v-if="waypoints.length > 0">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><Location /></el-icon>
            <span>控制点管理 ({{ waypoints.length }})</span>
            <el-button size="small" @click="addWaypoint">
              <el-icon><Plus /></el-icon>
              添加
            </el-button>
          </div>
        </template>

        <div class="waypoints-list">
          <div
            v-for="(point, index) in waypoints"
            :key="index"
            class="waypoint-item"
          >
            <div class="waypoint-info">
              <span class="waypoint-index">{{ index + 1 }}</span>
              <div class="waypoint-coords">
                <span>{{ point.longitude.toFixed(6) }}, {{ point.latitude.toFixed(6) }}</span>
                <span class="elevation">{{ point.elevation.toFixed(1) }}m</span>
              </div>
            </div>
            <div class="waypoint-actions">
              <el-button size="small" @click="editWaypoint(index)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button size="small" type="danger" @click="removeWaypoint(index)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 选线结果 -->
    <div class="alignment-results" v-if="alignmentResult">
      <el-card>
        <template #header>
          <div class="card-header">
            <el-icon><TrendCharts /></el-icon>
            <span>选线结果</span>
            <el-button size="small" type="success" @click="acceptAlignment">
              <el-icon><Check /></el-icon>
              采用此方案
            </el-button>
          </div>
        </template>

        <div class="result-stats">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="总长度" :value="alignmentResult.totalLength" suffix="m" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="水平曲线" :value="alignmentResult.horizontalCurves.length" suffix="个" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="竖曲线" :value="alignmentResult.verticalCurves.length" suffix="个" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="控制点" :value="alignmentResult.points.length" suffix="个" />
            </el-col>
          </el-row>
        </div>

        <div class="result-details">
          <el-tabs v-model="activeResultTab">
            <el-tab-pane label="线形要素" name="elements">
              <AlignmentElements :alignment="alignmentResult" />
            </el-tab-pane>
            <el-tab-pane label="技术指标" name="indicators">
              <TechnicalIndicators :alignment="alignmentResult" :standards="designParams" />
            </el-tab-pane>
            <el-tab-pane label="安全检查" name="safety">
              <SafetyCheck :alignment="alignmentResult" :standards="designParams" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { 
  Route, 
  Location, 
  MagicStick, 
  Delete, 
  Upload, 
  Plus, 
  Edit, 
  TrendCharts, 
  Check 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { RoadAlignmentAlgorithm } from '@/utils/roadDesignAlgorithms'
import type { RoadCenterline } from '@/types'

// 临时组件占位符
const AlignmentElements = { 
  template: '<div class="placeholder">线形要素表</div>',
  props: ['alignment']
}
const TechnicalIndicators = { 
  template: '<div class="placeholder">技术指标检查</div>',
  props: ['alignment', 'standards']
}
const SafetyCheck = { 
  template: '<div class="placeholder">安全检查结果</div>',
  props: ['alignment', 'standards']
}

interface Props {
  designParams: any
  terrainData: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'alignment-created': [alignment: RoadCenterline]
}>()

// 响应式数据
const generating = ref(false)
const activeResultTab = ref('elements')

// 选线参数
const alignmentParams = reactive({
  startPoint: {
    longitude: 116.3974,
    latitude: 39.9093,
    elevation: 50
  },
  endPoint: {
    longitude: 116.4074,
    latitude: 39.9193,
    elevation: 80
  },
  method: 'auto',
  objectives: ['length', 'earthwork'],
  constraints: {
    maxGrade: 8,
    minRadius: 50,
    minSightDistance: 100
  }
})

// 控制点
const waypoints = ref([
  {
    longitude: 116.4024,
    latitude: 39.9143,
    elevation: 65
  }
])

// 选线结果
const alignmentResult = ref<RoadCenterline | null>(null)

// 方法
async function generateAlignment() {
  generating.value = true
  
  try {
    // 模拟选线计算过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const algorithm = new RoadAlignmentAlgorithm(props.terrainData, props.designParams.roadType)
    
    const result = algorithm.generateAlignment(
      alignmentParams.startPoint,
      alignmentParams.endPoint,
      waypoints.value
    )
    
    alignmentResult.value = result
    ElMessage.success('道路选线生成成功')
  } catch (error) {
    console.error('选线生成失败:', error)
    ElMessage.error('选线生成失败')
  } finally {
    generating.value = false
  }
}

function clearAlignment() {
  alignmentResult.value = null
  ElMessage.info('已清除选线结果')
}

function importWaypoints() {
  ElMessage.info('导入控制点功能开发中...')
}

function addWaypoint() {
  const lastPoint = waypoints.value[waypoints.value.length - 1] || alignmentParams.startPoint
  waypoints.value.push({
    longitude: lastPoint.longitude + 0.001,
    latitude: lastPoint.latitude + 0.001,
    elevation: lastPoint.elevation + 5
  })
  ElMessage.success('已添加控制点')
}

function editWaypoint(index: number) {
  ElMessage.info(`编辑控制点 ${index + 1}`)
  // 实现编辑逻辑
}

function removeWaypoint(index: number) {
  waypoints.value.splice(index, 1)
  ElMessage.success('已删除控制点')
}

function acceptAlignment() {
  if (alignmentResult.value) {
    emit('alignment-created', alignmentResult.value)
    ElMessage.success('已采用此选线方案')
  }
}
</script>

<style scoped>
.road-alignment-design {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  height: 100%;
  overflow: auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.coordinate-input {
  display: flex;
  gap: var(--spacing-xs);
}

.coordinate-input .el-input-number {
  flex: 1;
}

.constraints-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.constraint-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.constraint-item span {
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

.waypoints-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.waypoint-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm);
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-base);
}

.waypoint-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.waypoint-index {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: bold;
}

.waypoint-coords {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.elevation {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

.waypoint-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.result-stats {
  margin-bottom: var(--spacing-lg);
}

.result-details {
  margin-top: var(--spacing-md);
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150px;
  background-color: var(--color-background-soft);
  border: 2px dashed var(--color-border);
  border-radius: var(--border-radius-base);
  color: var(--color-text-soft);
  font-style: italic;
}

/* Element Plus 样式覆盖 */
:deep(.el-statistic__content) {
  color: var(--color-text);
}

:deep(.el-statistic__number) {
  color: var(--color-primary);
}

:deep(.el-tabs__item) {
  color: var(--color-text-soft);
}

:deep(.el-tabs__item.is-active) {
  color: var(--color-primary);
}
</style>
