<template>
  <div class="safety-check-sidebar">
    <!-- 检测配置 -->
    <div class="check-config">
      <div class="section-title">
        <el-icon><Setting /></el-icon>
        <span>检测配置</span>
      </div>
      <div class="config-form">
        <div class="form-item">
          <span class="form-label">检测道路</span>
          <el-select v-model="checkConfig.selectedRoad" placeholder="选择道路" size="small">
            <el-option
              v-for="road in availableRoads"
              :key="road.id"
              :label="road.name"
              :value="road.id"
            />
          </el-select>
        </div>

        <div class="form-item">
          <span class="form-label">检测标准</span>
          <el-select v-model="checkConfig.standard" size="small">
            <el-option label="露天矿山标准" value="openpit" />
            <el-option label="公路工程标准" value="highway" />
            <el-option label="自定义标准" value="custom" />
          </el-select>
        </div>

        <div class="form-item">
          <span class="form-label">严格程度</span>
          <el-radio-group v-model="checkConfig.strictness" size="small">
            <el-radio label="strict">严格</el-radio>
            <el-radio label="normal">标准</el-radio>
            <el-radio label="loose">宽松</el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>

    <!-- 检测项目 -->
    <div class="check-items">
      <div class="section-title">
        <el-icon><List /></el-icon>
        <span>检测项目</span>
      </div>
      <div class="items-list">
        <div
          v-for="item in checkItems"
          :key="item.key"
          class="check-item"
          :class="{ active: checkConfig.selectedItems.includes(item.key) }"
          @click="toggleCheckItem(item.key)"
        >
          <div class="item-header">
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            <span class="item-name">{{ item.name }}</span>
            <el-checkbox
              :model-value="checkConfig.selectedItems.includes(item.key)"
              @change="toggleCheckItem(item.key)"
            />
          </div>
          <div class="item-description">{{ item.description }}</div>
          <div class="item-standard" v-if="item.standard">
            标准值: {{ item.standard }}
          </div>
        </div>
      </div>
    </div>

    <!-- 检测历史 -->
    <div class="check-history">
      <div class="section-title">
        <el-icon><Clock /></el-icon>
        <span>检测历史</span>
      </div>
      <div class="history-list">
        <div
          v-for="(history, index) in checkHistory"
          :key="index"
          class="history-item"
          @click="loadHistoryResult(history)"
        >
          <div class="history-header">
            <span class="history-road">{{ history.roadName }}</span>
            <el-tag :type="getScoreType(history.score)" size="small">
              {{ history.score }}分
            </el-tag>
          </div>
          <div class="history-details">
            <span class="history-time">{{ formatTime(history.time) }}</span>
            <span class="history-issues">{{ history.issues }}个问题</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 安全统计 -->
    <div class="safety-stats">
      <div class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        <span>安全统计</span>
      </div>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">{{ safetyStats.totalChecks }}</div>
          <div class="stat-label">总检测次数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ safetyStats.avgScore }}</div>
          <div class="stat-label">平均安全分</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ safetyStats.criticalIssues }}</div>
          <div class="stat-label">严重问题</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ safetyStats.resolvedIssues }}</div>
          <div class="stat-label">已解决问题</div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="section-title">
        <el-icon><Lightning /></el-icon>
        <span>快速操作</span>
      </div>
      <div class="action-buttons">
        <el-button size="small" type="primary" @click="startQuickCheck">
          <el-icon><Search /></el-icon>
          快速检测
        </el-button>
        <el-button size="small" @click="batchCheck">
          <el-icon><Operation /></el-icon>
          批量检测
        </el-button>
        <el-button size="small" @click="exportResults">
          <el-icon><Download /></el-icon>
          导出结果
        </el-button>
        <el-button size="small" @click="clearHistory">
          <el-icon><Delete /></el-icon>
          清空历史
        </el-button>
      </div>
    </div>

    <!-- 安全建议 -->
    <div class="safety-recommendations" v-if="recommendations.length > 0">
      <div class="section-title">
        <el-icon><Warning /></el-icon>
        <span>安全建议</span>
      </div>
      <div class="recommendations-list">
        <div
          v-for="(rec, index) in recommendations"
          :key="index"
          class="recommendation-item"
          :class="rec.priority"
        >
          <div class="rec-header">
            <el-icon>
              <Warning v-if="rec.priority === 'high'" />
              <InfoFilled v-else />
            </el-icon>
            <span class="rec-title">{{ rec.title }}</span>
          </div>
          <div class="rec-content">{{ rec.content }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import {
  Setting,
  List,
  Clock,
  DataAnalysis,
  Lightning,
  Search,
  Operation,
  Download,
  Delete,
  Warning,
  InfoFilled,
  TrendCharts,
  View,
  Shield
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const checkConfig = reactive({
  selectedRoad: '',
  standard: 'openpit',
  strictness: 'normal',
  selectedItems: ['grade', 'radius', 'sight_distance', 'width']
})

// 可用道路
const availableRoads = ref([
  { id: '1', name: '主干道A' },
  { id: '2', name: '分支道路B' },
  { id: '3', name: '运输道路C' }
])

// 检测项目
const checkItems = ref([
  {
    key: 'grade',
    name: '坡度检测',
    description: '检查道路纵坡是否符合标准',
    standard: '≤8%',
    icon: 'TrendCharts'
  },
  {
    key: 'radius',
    name: '转弯半径',
    description: '检查最小转弯半径',
    standard: '≥50m',
    icon: 'View'
  },
  {
    key: 'sight_distance',
    name: '视距检测',
    description: '检查停车视距和超车视距',
    standard: '≥100m',
    icon: 'View'
  },
  {
    key: 'width',
    name: '道路宽度',
    description: '检查行车道和路肩宽度',
    standard: '≥7m',
    icon: 'Operation'
  },
  {
    key: 'clearance',
    name: '净空检测',
    description: '检查垂直和水平净空',
    standard: '≥5m',
    icon: 'Shield'
  },
  {
    key: 'drainage',
    name: '排水检测',
    description: '检查横坡和排水设施',
    standard: '2-4%',
    icon: 'Operation'
  }
])

// 检测历史
const checkHistory = ref([
  {
    roadName: '主干道A',
    score: 85,
    issues: 3,
    time: new Date(Date.now() - 3600000).toISOString()
  },
  {
    roadName: '分支道路B',
    score: 72,
    issues: 5,
    time: new Date(Date.now() - 7200000).toISOString()
  },
  {
    roadName: '运输道路C',
    score: 91,
    issues: 1,
    time: new Date(Date.now() - 10800000).toISOString()
  }
])

// 安全统计
const safetyStats = reactive({
  totalChecks: 15,
  avgScore: 82,
  criticalIssues: 2,
  resolvedIssues: 8
})

// 安全建议
const recommendations = ref([
  {
    priority: 'high',
    title: '坡度过大',
    content: '主干道A存在坡度超标问题，建议调整线形设计'
  },
  {
    priority: 'medium',
    title: '转弯半径偏小',
    content: '分支道路B的转弯半径接近下限，建议适当增大'
  }
])

// 方法
function toggleCheckItem(key: string) {
  const index = checkConfig.selectedItems.indexOf(key)
  if (index > -1) {
    checkConfig.selectedItems.splice(index, 1)
  } else {
    checkConfig.selectedItems.push(key)
  }
}

function loadHistoryResult(history: any) {
  ElMessage.info(`加载 ${history.roadName} 的检测结果`)
}

function getScoreType(score: number): string {
  if (score >= 90) return 'success'
  if (score >= 70) return 'warning'
  return 'danger'
}

function formatTime(timeStr: string): string {
  const now = new Date()
  const time = new Date(timeStr)
  const diff = now.getTime() - time.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) return '刚刚'
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

function startQuickCheck() {
  if (!checkConfig.selectedRoad) {
    ElMessage.warning('请先选择要检测的道路')
    return
  }
  ElMessage.info('开始快速安全检测...')
}

function batchCheck() {
  ElMessage.info('批量检测功能开发中...')
}

function exportResults() {
  ElMessage.success('检测结果导出成功')
}

function clearHistory() {
  checkHistory.value = []
  ElMessage.success('检测历史已清空')
}
</script>

<style scoped>
.safety-check-sidebar {
  height: 100%;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: bold;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--color-border);
}

.check-config,
.check-items,
.check-history,
.safety-stats,
.quick-actions,
.safety-recommendations {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-sm);
  background-color: var(--color-background);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--color-border);
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-label {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  font-weight: 500;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.check-item {
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: all 0.3s ease;
}

.check-item:hover {
  background-color: var(--color-background-soft);
  border-color: var(--color-primary);
}

.check-item.active {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-color: var(--color-primary);
}

.item-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.item-name {
  flex: 1;
  font-weight: 500;
  color: var(--color-text);
}

.item-description {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
  margin-bottom: var(--spacing-xs);
}

.item-standard {
  font-size: var(--font-size-xs);
  color: var(--color-primary);
  font-weight: 500;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.history-item {
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.history-item:hover {
  background-color: var(--color-background-soft);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.history-road {
  font-weight: 500;
  color: var(--color-text);
}

.history-details {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-sm);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-sm);
  background-color: var(--color-background-soft);
  border-radius: var(--border-radius-small);
}

.stat-number {
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--color-primary);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
  margin-top: var(--spacing-xs);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.action-buttons .el-button {
  justify-content: flex-start;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.recommendation-item {
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-small);
  border-left: 3px solid;
}

.recommendation-item.high {
  border-left-color: var(--color-danger);
  background-color: rgba(245, 108, 108, 0.05);
}

.recommendation-item.medium {
  border-left-color: var(--color-warning);
  background-color: rgba(230, 162, 60, 0.05);
}

.rec-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.rec-title {
  font-weight: 500;
  color: var(--color-text);
}

.rec-content {
  font-size: var(--font-size-sm);
  color: var(--color-text-soft);
  line-height: 1.4;
}

/* Element Plus 样式覆盖 */
:deep(.el-select) {
  width: 100%;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

:deep(.el-radio) {
  margin-right: 0;
}

/* 滚动条样式 */
.safety-check-sidebar::-webkit-scrollbar {
  width: 4px;
}

.safety-check-sidebar::-webkit-scrollbar-track {
  background: var(--color-background);
}

.safety-check-sidebar::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 2px;
}

.safety-check-sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}
</style>
