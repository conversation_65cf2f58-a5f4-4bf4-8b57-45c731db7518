<template>
  <div class="gis-view">
    <!-- 工具栏 -->
    <div class="gis-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button size="small" @click="resetView">
            <el-icon><Refresh /></el-icon>
            重置视图
          </el-button>
          <el-button size="small" @click="fitToData">
            <el-icon><FullScreen /></el-icon>
            适应数据
          </el-button>
          <el-button size="small" @click="takeScreenshot">
            <el-icon><Camera /></el-icon>
            截图
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-center">
        <el-select v-model="currentView" size="small" style="width: 120px" @change="handleViewChange">
          <el-option label="三维视图" value="3d" />
          <el-option label="二维视图" value="2d" />
        </el-select>
      </div>

      <div class="toolbar-right">
        <el-button size="small" @click="showLayerPanel">
          <el-icon><Grid /></el-icon>
          图层管理
        </el-button>
        <el-button size="small" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- Cesium 3D视图 -->
    <div class="cesium-wrapper">
      <CesiumViewer
        ref="cesiumViewerRef"
        :initial-position="initialPosition"
        @viewer-ready="handleViewerReady"
        @position-changed="handlePositionChanged"
        @data-clicked="handleDataClicked"
      />
    </div>

    <!-- 图层面板 -->
    <el-drawer
      v-model="layerPanelVisible"
      title="图层管理"
      direction="rtl"
      size="300px"
    >
      <LayerPanel />
    </el-drawer>

    <!-- 数据详情面板 -->
    <el-drawer
      v-model="dataDetailVisible"
      title="数据详情"
      direction="ltr"
      size="400px"
    >
      <div v-if="selectedData" class="data-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="名称">{{ selectedData.name }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ selectedData.type }}</el-descriptions-item>
          <el-descriptions-item label="大小">{{ selectedData.size }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ selectedData.createTime }}</el-descriptions-item>
          <el-descriptions-item label="坐标系">{{ selectedData.coordinateSystem }}</el-descriptions-item>
        </el-descriptions>

        <div class="data-actions">
          <el-button type="primary" @click="editData">编辑</el-button>
          <el-button type="warning" @click="exportSingleData">导出</el-button>
          <el-button type="danger" @click="deleteData">删除</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useGISStore } from '@/stores/gis'
import {
  Refresh,
  FullScreen,
  Camera,
  Download,
  Grid
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import CesiumViewer from '@/components/cesium/CesiumViewer.vue'
import LayerPanel from '@/components/common/LayerPanel.vue'
import type { GISData } from '@/types'

const gisStore = useGISStore()

// 响应式数据
const cesiumViewerRef = ref()
const currentView = ref('3d')
const layerPanelVisible = ref(false)
const dataDetailVisible = ref(false)
const selectedData = ref<GISData | null>(null)

// 初始位置（北京）
const initialPosition = {
  longitude: 116.3974,
  latitude: 39.9093,
  height: 1000
}

onMounted(() => {
  // 初始化示例数据
  if (gisStore.allData.length === 0) {
    gisStore.initSampleData()
  }
})

// 事件处理
function handleViewerReady(viewer: any) {
  console.log('Cesium viewer ready:', viewer)
  ElMessage.success('GIS视图初始化完成')
}

function handlePositionChanged(position: any) {
  // 可以在这里更新位置信息到状态栏
  console.log('Position changed:', position)
}

function handleDataClicked(data: any) {
  console.log('Data clicked:', data)
  // 显示数据详情
  if (data.id && data.id.id) {
    const gisData = gisStore.getDataById(data.id.id)
    if (gisData) {
      selectedData.value = gisData
      dataDetailVisible.value = true
    }
  }
}

function handleViewChange(view: string) {
  if (cesiumViewerRef.value) {
    if (view === '2d') {
      cesiumViewerRef.value.toggle2D3D()
    }
  }
}

// 工具栏方法
function resetView() {
  if (cesiumViewerRef.value) {
    cesiumViewerRef.value.goHome()
  }
}

function fitToData() {
  ElMessage.info('适应数据功能开发中...')
}

function takeScreenshot() {
  if (cesiumViewerRef.value && cesiumViewerRef.value.viewer) {
    const viewer = cesiumViewerRef.value.viewer
    viewer.render()
    const canvas = viewer.scene.canvas
    const image = canvas.toDataURL('image/png')

    // 创建下载链接
    const link = document.createElement('a')
    link.download = `gis_screenshot_${new Date().getTime()}.png`
    link.href = image
    link.click()

    ElMessage.success('截图已保存')
  }
}

function showLayerPanel() {
  layerPanelVisible.value = true
}

function exportData() {
  const visibleData = gisStore.allData.filter(data => data.visible)
  if (visibleData.length === 0) {
    ElMessage.warning('没有可见的数据可以导出')
    return
  }

  ElMessage.success(`导出 ${visibleData.length} 个数据文件`)
  // 这里实现实际的导出逻辑
}

// 数据操作方法
function editData() {
  if (selectedData.value) {
    ElMessage.info(`编辑数据: ${selectedData.value.name}`)
    // 这里实现编辑逻辑
  }
}

function exportSingleData() {
  if (selectedData.value) {
    ElMessage.success(`导出数据: ${selectedData.value.name}`)
    // 这里实现单个数据导出逻辑
  }
}

function deleteData() {
  if (!selectedData.value) return

  ElMessageBox.confirm(
    `确定要删除数据 "${selectedData.value.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    if (selectedData.value) {
      gisStore.removeData(selectedData.value.id)
      dataDetailVisible.value = false
      selectedData.value = null
      ElMessage.success('数据已删除')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}
</script>

<style scoped>
.gis-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
}

.gis-toolbar {
  height: var(--toolbar-height);
  background-color: var(--color-background-mute);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  gap: var(--spacing-md);
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.cesium-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.data-detail {
  padding: var(--spacing-md);
}

.data-actions {
  margin-top: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-sm);
}

.data-actions .el-button {
  flex: 1;
}
</style>
