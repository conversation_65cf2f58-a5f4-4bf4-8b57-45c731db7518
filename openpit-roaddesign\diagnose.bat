@echo off
chcp 65001 >nul
title 露天矿山道路设计软件系统诊断

echo.
echo ========================================
echo   露天矿山道路设计软件系统诊断 v1.0
echo ========================================
echo.

echo 🔍 开始系统诊断...
echo.

:: 创建诊断报告文件
set REPORT_FILE=diagnostic_report_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
set REPORT_FILE=%REPORT_FILE: =0%

echo 生成诊断报告: %REPORT_FILE%
echo ======================================== > %REPORT_FILE%
echo 露天矿山道路设计软件系统诊断报告 >> %REPORT_FILE%
echo 生成时间: %date% %time% >> %REPORT_FILE%
echo ======================================== >> %REPORT_FILE%
echo. >> %REPORT_FILE%

:: 系统信息
echo [1/10] 收集系统信息...
echo 1. 系统信息 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
systeminfo | findstr /C:"OS Name" /C:"OS Version" /C:"System Type" /C:"Total Physical Memory" >> %REPORT_FILE%
echo. >> %REPORT_FILE%

:: Node.js环境
echo [2/10] 检查Node.js环境...
echo 2. Node.js环境 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
node --version >> %REPORT_FILE% 2>&1
npm --version >> %REPORT_FILE% 2>&1
echo Node.js安装路径: >> %REPORT_FILE%
where node >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

:: 项目信息
echo [3/10] 检查项目信息...
echo 3. 项目信息 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
if exist "package.json" (
    echo ✅ package.json存在 >> %REPORT_FILE%
    findstr /C:"name" /C:"version" /C:"scripts" package.json >> %REPORT_FILE%
) else (
    echo ❌ package.json不存在 >> %REPORT_FILE%
)
echo. >> %REPORT_FILE%

:: 依赖检查
echo [4/10] 检查项目依赖...
echo 4. 项目依赖 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
if exist "node_modules" (
    echo ✅ node_modules目录存在 >> %REPORT_FILE%
    for /f %%i in ('dir /ad node_modules ^| find /c "个目录"') do echo 依赖包数量: %%i >> %REPORT_FILE%
) else (
    echo ❌ node_modules目录不存在 >> %REPORT_FILE%
)

if exist "package-lock.json" (
    echo ✅ package-lock.json存在 >> %REPORT_FILE%
) else (
    echo ❌ package-lock.json不存在 >> %REPORT_FILE%
)
echo. >> %REPORT_FILE%

:: 项目文件结构
echo [5/10] 检查项目文件结构...
echo 5. 项目文件结构 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
if exist "src" echo ✅ src目录存在 >> %REPORT_FILE%
if exist "public" echo ✅ public目录存在 >> %REPORT_FILE%
if exist "vite.config.ts" echo ✅ vite.config.ts存在 >> %REPORT_FILE%
if exist "tsconfig.json" echo ✅ tsconfig.json存在 >> %REPORT_FILE%
if exist ".env" echo ✅ .env存在 >> %REPORT_FILE%
if exist "vitest.config.ts" echo ✅ vitest.config.ts存在 >> %REPORT_FILE%
echo. >> %REPORT_FILE%

:: 端口检查
echo [6/10] 检查端口占用...
echo 6. 端口占用情况 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
netstat -an | findstr ":3000" >> %REPORT_FILE%
netstat -an | findstr ":24678" >> %REPORT_FILE%
echo. >> %REPORT_FILE%

:: 网络连接
echo [7/10] 检查网络连接...
echo 7. 网络连接 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
ping -n 1 www.baidu.com >> %REPORT_FILE% 2>&1
ping -n 1 registry.npmjs.org >> %REPORT_FILE% 2>&1
echo. >> %REPORT_FILE%

:: 磁盘空间
echo [8/10] 检查磁盘空间...
echo 8. 磁盘空间 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
for %%i in (C: D: E:) do (
    if exist %%i\ (
        for /f "tokens=3" %%j in ('dir %%i\ ^| find "可用字节"') do echo %%i 可用空间: %%j 字节 >> %REPORT_FILE%
    )
)
echo. >> %REPORT_FILE%

:: 环境变量
echo [9/10] 检查环境变量...
echo 9. 相关环境变量 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
echo PATH: >> %REPORT_FILE%
echo %PATH% >> %REPORT_FILE%
echo NODE_ENV: %NODE_ENV% >> %REPORT_FILE%
echo TEMP: %TEMP% >> %REPORT_FILE%
echo. >> %REPORT_FILE%

:: 最近的错误日志
echo [10/10] 收集错误信息...
echo 10. 最近的错误信息 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%
if exist "npm-debug.log" (
    echo NPM调试日志: >> %REPORT_FILE%
    type npm-debug.log >> %REPORT_FILE%
) else (
    echo 未找到npm-debug.log >> %REPORT_FILE%
)

if exist ".vite\deps\_metadata.json" (
    echo Vite依赖元数据存在 >> %REPORT_FILE%
) else (
    echo Vite依赖元数据不存在 >> %REPORT_FILE%
)
echo. >> %REPORT_FILE%

:: 诊断建议
echo 11. 诊断建议 >> %REPORT_FILE%
echo ---------------------------------------- >> %REPORT_FILE%

:: 检查Node.js版本
for /f "tokens=1 delims=v" %%i in ('node --version') do set NODE_VER=%%i
if "%NODE_VER%" LSS "16" (
    echo ⚠️  建议升级Node.js到16.0以上版本 >> %REPORT_FILE%
)

:: 检查内存
for /f "tokens=4" %%i in ('systeminfo ^| findstr "Total Physical Memory"') do (
    set MEM=%%i
    if "%%i" LSS "4,000" echo ⚠️  系统内存较少，建议至少4GB >> %REPORT_FILE%
)

:: 检查磁盘空间
for /f "tokens=3" %%i in ('dir C:\ ^| find "可用字节"') do (
    if %%i LSS 1073741824 echo ⚠️  C盘空间不足，建议至少保留1GB空间 >> %REPORT_FILE%
)

echo ✅ 诊断完成，请查看详细报告: %REPORT_FILE% >> %REPORT_FILE%

echo.
echo ✅ 系统诊断完成！
echo.
echo 📋 诊断报告已保存到: %REPORT_FILE%
echo.
echo 📊 诊断摘要:
echo    - 系统信息: 已收集
echo    - Node.js环境: 已检查
echo    - 项目依赖: 已验证
echo    - 网络连接: 已测试
echo    - 磁盘空间: 已检查
echo.
echo 💡 如果遇到问题，请将诊断报告发送给技术支持
echo.

:: 询问是否打开报告
set /p OPEN_REPORT=是否打开诊断报告？(y/n): 
if /i "%OPEN_REPORT%"=="y" (
    start notepad %REPORT_FILE%
)

pause
