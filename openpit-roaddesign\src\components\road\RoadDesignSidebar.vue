<template>
  <div class="road-design-sidebar">
    <!-- 设计工具 -->
    <div class="design-tools">
      <div class="section-title">
        <el-icon><MagicStick /></el-icon>
        <span>设计工具</span>
      </div>
      <div class="tool-buttons">
        <el-button 
          size="small" 
          :type="activeTool === 'alignment' ? 'primary' : 'default'"
          @click="setActiveTool('alignment')"
        >
          <el-icon><Route /></el-icon>
          道路选线
        </el-button>
        <el-button 
          size="small"
          :type="activeTool === 'profile' ? 'primary' : 'default'"
          @click="setActiveTool('profile')"
        >
          <el-icon><TrendCharts /></el-icon>
          纵断面设计
        </el-button>
        <el-button 
          size="small"
          :type="activeTool === 'cross' ? 'primary' : 'default'"
          @click="setActiveTool('cross')"
        >
          <el-icon><Grid /></el-icon>
          横断面设计
        </el-button>
        <el-button 
          size="small"
          :type="activeTool === 'optimize' ? 'primary' : 'default'"
          @click="setActiveTool('optimize')"
        >
          <el-icon><Setting /></el-icon>
          路线优化
        </el-button>
      </div>
    </div>

    <!-- 道路类型选择 -->
    <div class="road-type-selection">
      <div class="section-title">
        <el-icon><Document /></el-icon>
        <span>道路类型</span>
      </div>
      <div class="type-options">
        <el-radio-group v-model="selectedRoadType" @change="handleRoadTypeChange">
          <el-radio label="main">
            <div class="type-option">
              <span class="type-name">主要道路</span>
              <span class="type-desc">设计速度40km/h，最大坡度8%</span>
            </div>
          </el-radio>
          <el-radio label="branch">
            <div class="type-option">
              <span class="type-name">分支道路</span>
              <span class="type-desc">设计速度30km/h，最大坡度10%</span>
            </div>
          </el-radio>
          <el-radio label="access">
            <div class="type-option">
              <span class="type-name">通道道路</span>
              <span class="type-desc">设计速度20km/h，最大坡度12%</span>
            </div>
          </el-radio>
          <el-radio label="haul">
            <div class="type-option">
              <span class="type-name">运输道路</span>
              <span class="type-desc">设计速度50km/h，最大坡度6%</span>
            </div>
          </el-radio>
        </el-radio-group>
      </div>
    </div>

    <!-- 设计参数 -->
    <div class="design-parameters">
      <div class="section-title">
        <el-icon><Operation /></el-icon>
        <span>设计参数</span>
      </div>
      <div class="param-list">
        <div class="param-item">
          <span class="param-label">道路宽度</span>
          <el-input-number
            v-model="designParams.width"
            :min="3"
            :max="20"
            :step="0.5"
            size="small"
            controls-position="right"
          />
          <span class="param-unit">m</span>
        </div>
        <div class="param-item">
          <span class="param-label">设计速度</span>
          <el-input-number
            v-model="designParams.designSpeed"
            :min="10"
            :max="80"
            :step="5"
            size="small"
            controls-position="right"
          />
          <span class="param-unit">km/h</span>
        </div>
        <div class="param-item">
          <span class="param-label">最大坡度</span>
          <el-input-number
            v-model="designParams.maxGrade"
            :min="1"
            :max="15"
            :step="0.5"
            size="small"
            controls-position="right"
          />
          <span class="param-unit">%</span>
        </div>
        <div class="param-item">
          <span class="param-label">最小半径</span>
          <el-input-number
            v-model="designParams.minRadius"
            :min="10"
            :max="200"
            :step="5"
            size="small"
            controls-position="right"
          />
          <span class="param-unit">m</span>
        </div>
      </div>
    </div>

    <!-- 当前设计方案 -->
    <div class="current-design" v-if="currentDesign">
      <div class="section-title">
        <el-icon><DataLine /></el-icon>
        <span>当前方案</span>
      </div>
      <div class="design-info">
        <div class="design-header">
          <span class="design-name">{{ currentDesign.name }}</span>
          <el-tag :type="getDesignStatusType(currentDesign.status)" size="small">
            {{ currentDesign.status }}
          </el-tag>
        </div>
        <div class="design-stats">
          <div class="stat-item">
            <span class="stat-label">总长度:</span>
            <span class="stat-value">{{ currentDesign.length.toFixed(0) }}m</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最大坡度:</span>
            <span class="stat-value">{{ currentDesign.maxGrade.toFixed(1) }}%</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最小半径:</span>
            <span class="stat-value">{{ currentDesign.minRadius.toFixed(0) }}m</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">土方量:</span>
            <span class="stat-value">{{ formatVolume(currentDesign.earthwork) }}</span>
          </div>
        </div>
        <div class="design-actions">
          <el-button size="small" @click="editDesign">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-button size="small" @click="checkSafety">
            <el-icon><Shield /></el-icon>
            检测
          </el-button>
          <el-button size="small" @click="exportDesign">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>
    </div>

    <!-- 设计历史 -->
    <div class="design-history">
      <div class="section-title">
        <el-icon><Clock /></el-icon>
        <span>设计历史</span>
      </div>
      <div class="history-list">
        <div
          v-for="(design, index) in designHistory"
          :key="index"
          class="history-item"
          :class="{ active: currentDesign?.id === design.id }"
          @click="selectDesign(design)"
        >
          <div class="history-header">
            <span class="history-name">{{ design.name }}</span>
            <span class="history-time">{{ formatTime(design.createTime) }}</span>
          </div>
          <div class="history-details">
            <span>{{ design.length.toFixed(0) }}m</span>
            <el-tag :type="getDesignStatusType(design.status)" size="small">
              {{ design.status }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-operations">
      <div class="section-title">
        <el-icon><Lightning /></el-icon>
        <span>快速操作</span>
      </div>
      <div class="operation-buttons">
        <el-button size="small" @click="quickDesign">
          <el-icon><MagicStick /></el-icon>
          快速设计
        </el-button>
        <el-button size="small" @click="importTemplate">
          <el-icon><Upload /></el-icon>
          导入模板
        </el-button>
        <el-button size="small" @click="saveTemplate">
          <el-icon><FolderAdd /></el-icon>
          保存模板
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import {
  MagicStick,
  Route,
  TrendCharts,
  Grid,
  Setting,
  Document,
  Operation,
  DataLine,
  Edit,
  Shield,
  Download,
  Clock,
  Lightning,
  Upload,
  FolderAdd
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { OPENPIT_ROAD_STANDARDS } from '@/config'

// 响应式数据
const activeTool = ref('alignment')
const selectedRoadType = ref('main')

// 设计参数
const designParams = reactive({
  width: 7,
  designSpeed: 40,
  maxGrade: 8,
  minRadius: 50
})

// 当前设计方案
const currentDesign = ref({
  id: '1',
  name: '主干道方案A',
  status: 'completed',
  length: 1250,
  maxGrade: 6.5,
  minRadius: 60,
  earthwork: 15000,
  createTime: new Date().toISOString()
})

// 设计历史
const designHistory = ref([
  {
    id: '1',
    name: '主干道方案A',
    status: 'completed',
    length: 1250,
    createTime: new Date(Date.now() - 3600000).toISOString()
  },
  {
    id: '2',
    name: '主干道方案B',
    status: 'draft',
    length: 1180,
    createTime: new Date(Date.now() - 7200000).toISOString()
  },
  {
    id: '3',
    name: '分支道路C',
    status: 'warning',
    length: 850,
    createTime: new Date(Date.now() - 10800000).toISOString()
  }
])

// 方法
function setActiveTool(tool: string) {
  activeTool.value = tool
  ElMessage.info(`已切换到${getToolName(tool)}工具`)
}

function getToolName(tool: string): string {
  const names = {
    alignment: '道路选线',
    profile: '纵断面设计',
    cross: '横断面设计',
    optimize: '路线优化'
  }
  return names[tool as keyof typeof names] || tool
}

function handleRoadTypeChange(type: string) {
  const standards = OPENPIT_ROAD_STANDARDS[type]
  if (standards) {
    designParams.width = standards.laneWidth * 2 + standards.shoulderWidth * 2
    designParams.designSpeed = standards.designSpeed
    designParams.maxGrade = standards.maxGrade
    designParams.minRadius = standards.minRadius
  }
  ElMessage.success(`已切换到${getRoadTypeName(type)}标准`)
}

function getRoadTypeName(type: string): string {
  const names = {
    main: '主要道路',
    branch: '分支道路',
    access: '通道道路',
    haul: '运输道路'
  }
  return names[type as keyof typeof names] || type
}

function getDesignStatusType(status: string): string {
  const types = {
    completed: 'success',
    draft: 'info',
    warning: 'warning',
    error: 'danger'
  }
  return types[status as keyof typeof types] || 'info'
}

function formatVolume(volume: number): string {
  if (volume >= 10000) {
    return `${(volume / 10000).toFixed(1)}万m³`
  }
  return `${volume.toFixed(0)}m³`
}

function formatTime(timeStr: string): string {
  const now = new Date()
  const time = new Date(timeStr)
  const diff = now.getTime() - time.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) return '刚刚'
  if (hours < 24) return `${hours}小时前`
  
  const days = Math.floor(hours / 24)
  return `${days}天前`
}

function selectDesign(design: any) {
  currentDesign.value = design
  ElMessage.success(`已选择设计方案: ${design.name}`)
}

function editDesign() {
  ElMessage.info('编辑设计方案功能开发中...')
}

function checkSafety() {
  ElMessage.info('安全检测功能开发中...')
}

function exportDesign() {
  ElMessage.success('设计方案导出成功')
}

function quickDesign() {
  ElMessage.info('快速设计功能开发中...')
}

function importTemplate() {
  ElMessage.info('导入模板功能开发中...')
}

function saveTemplate() {
  ElMessage.success('模板保存成功')
}
</script>

<style scoped>
.road-design-sidebar {
  height: 100%;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: bold;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--color-border);
}

.design-tools,
.road-type-selection,
.design-parameters,
.current-design,
.design-history,
.quick-operations {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-sm);
  background-color: var(--color-background);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--color-border);
}

.tool-buttons,
.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.tool-buttons .el-button,
.operation-buttons .el-button {
  justify-content: flex-start;
}

.type-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.type-option {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.type-name {
  font-weight: 500;
  color: var(--color-text);
}

.type-desc {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

.param-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.param-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.param-label {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--color-text);
}

.param-unit {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
  min-width: 30px;
}

.design-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.design-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.design-name {
  font-weight: bold;
  color: var(--color-text);
}

.design-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-sm);
}

.stat-label {
  color: var(--color-text-soft);
}

.stat-value {
  color: var(--color-text);
  font-weight: 500;
}

.design-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.design-actions .el-button {
  flex: 1;
  padding: 4px 8px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.history-item {
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-small);
  cursor: pointer;
  transition: background-color 0.3s ease;
  border: 1px solid transparent;
}

.history-item:hover {
  background-color: var(--color-background-soft);
}

.history-item.active {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-color: var(--color-primary);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.history-name {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  font-weight: 500;
}

.history-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

.history-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
}

/* Element Plus 样式覆盖 */
:deep(.el-radio) {
  width: 100%;
  margin-right: 0;
  margin-bottom: var(--spacing-sm);
}

:deep(.el-radio__label) {
  width: 100%;
  padding-left: var(--spacing-xs);
}

:deep(.el-input-number) {
  width: 80px;
}

/* 滚动条样式 */
.road-design-sidebar::-webkit-scrollbar {
  width: 4px;
}

.road-design-sidebar::-webkit-scrollbar-track {
  background: var(--color-background);
}

.road-design-sidebar::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 2px;
}

.road-design-sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}
</style>
