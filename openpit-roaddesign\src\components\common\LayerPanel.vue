<template>
  <div class="layer-panel">
    <div class="panel-header">
      <h3>图层管理</h3>
      <el-button size="small" @click="refreshLayers">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <div class="panel-content">
      <!-- 基础图层 -->
      <div class="layer-group">
        <div class="group-header" @click="toggleGroup('base')">
          <el-icon><CaretRight v-if="!expandedGroups.base" /><CaretBottom v-else /></el-icon>
          <span>基础图层</span>
        </div>
        <div v-show="expandedGroups.base" class="group-content">
          <div 
            v-for="layer in baseLayers" 
            :key="layer.id"
            class="layer-item"
            :class="{ active: layer.visible }"
          >
            <el-checkbox 
              v-model="layer.visible" 
              @change="toggleLayerVisibility(layer)"
            >
              {{ layer.name }}
            </el-checkbox>
            <div class="layer-controls">
              <el-slider
                v-model="layer.opacity"
                :min="0"
                :max="100"
                size="small"
                @change="updateLayerOpacity(layer)"
                style="width: 60px;"
              />
              <span class="opacity-value">{{ layer.opacity }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 地形图层 -->
      <div class="layer-group">
        <div class="group-header" @click="toggleGroup('terrain')">
          <el-icon><CaretRight v-if="!expandedGroups.terrain" /><CaretBottom v-else /></el-icon>
          <span>地形图层</span>
        </div>
        <div v-show="expandedGroups.terrain" class="group-content">
          <div 
            v-for="layer in terrainLayers" 
            :key="layer.id"
            class="layer-item"
            :class="{ active: layer.visible }"
          >
            <el-checkbox 
              v-model="layer.visible" 
              @change="toggleLayerVisibility(layer)"
            >
              {{ layer.name }}
            </el-checkbox>
            <div class="layer-controls">
              <el-slider
                v-model="layer.opacity"
                :min="0"
                :max="100"
                size="small"
                @change="updateLayerOpacity(layer)"
                style="width: 60px;"
              />
              <span class="opacity-value">{{ layer.opacity }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据图层 -->
      <div class="layer-group">
        <div class="group-header" @click="toggleGroup('data')">
          <el-icon><CaretRight v-if="!expandedGroups.data" /><CaretBottom v-else /></el-icon>
          <span>数据图层</span>
        </div>
        <div v-show="expandedGroups.data" class="group-content">
          <div 
            v-for="layer in dataLayers" 
            :key="layer.id"
            class="layer-item"
            :class="{ active: layer.visible }"
          >
            <el-checkbox 
              v-model="layer.visible" 
              @change="toggleLayerVisibility(layer)"
            >
              {{ layer.name }}
            </el-checkbox>
            <div class="layer-controls">
              <el-slider
                v-model="layer.opacity"
                :min="0"
                :max="100"
                size="small"
                @change="updateLayerOpacity(layer)"
                style="width: 60px;"
              />
              <span class="opacity-value">{{ layer.opacity }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-footer">
      <el-button size="small" @click="showAllLayers">显示全部</el-button>
      <el-button size="small" @click="hideAllLayers">隐藏全部</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Refresh, CaretRight, CaretBottom } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

interface Layer {
  id: string
  name: string
  type: string
  visible: boolean
  opacity: number
}

// 展开状态
const expandedGroups = reactive({
  base: true,
  terrain: true,
  data: true
})

// 图层数据
const baseLayers = ref<Layer[]>([
  { id: 'satellite', name: '卫星影像', type: 'imagery', visible: true, opacity: 100 },
  { id: 'terrain_map', name: '地形图', type: 'imagery', visible: false, opacity: 80 },
  { id: 'street', name: '街道地图', type: 'imagery', visible: false, opacity: 90 }
])

const terrainLayers = ref<Layer[]>([
  { id: 'world_terrain', name: '世界地形', type: 'terrain', visible: true, opacity: 100 },
  { id: 'local_terrain', name: '本地地形', type: 'terrain', visible: false, opacity: 85 }
])

const dataLayers = ref<Layer[]>([
  { id: 'drilling_data', name: '钻孔数据', type: 'data', visible: true, opacity: 90 },
  { id: 'geology_data', name: '地质数据', type: 'data', visible: false, opacity: 75 },
  { id: 'road_data', name: '道路数据', type: 'data', visible: true, opacity: 95 }
])

// 方法
function toggleGroup(groupName: keyof typeof expandedGroups) {
  expandedGroups[groupName] = !expandedGroups[groupName]
}

function toggleLayerVisibility(layer: Layer) {
  ElMessage.info(`${layer.visible ? '显示' : '隐藏'}图层: ${layer.name}`)
  // 这里应该调用实际的图层显示/隐藏逻辑
}

function updateLayerOpacity(layer: Layer) {
  ElMessage.info(`更新图层透明度: ${layer.name} - ${layer.opacity}%`)
  // 这里应该调用实际的图层透明度更新逻辑
}

function refreshLayers() {
  ElMessage.success('图层列表已刷新')
  // 这里应该重新加载图层数据
}

function showAllLayers() {
  [...baseLayers.value, ...terrainLayers.value, ...dataLayers.value].forEach(layer => {
    layer.visible = true
  })
  ElMessage.success('已显示所有图层')
}

function hideAllLayers() {
  [...baseLayers.value, ...terrainLayers.value, ...dataLayers.value].forEach(layer => {
    layer.visible = false
  })
  ElMessage.success('已隐藏所有图层')
}
</script>

<style scoped>
.layer-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-background-mute);
}

.panel-header h3 {
  margin: 0;
  color: var(--color-primary);
  font-size: var(--font-size-lg);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.layer-group {
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-base);
  background-color: var(--color-background-soft);
}

.group-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  font-weight: bold;
  color: var(--color-text);
  background-color: var(--color-background-mute);
  border-bottom: 1px solid var(--color-border);
}

.group-header:hover {
  background-color: var(--color-background-light);
}

.group-content {
  padding: var(--spacing-sm);
}

.layer-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xs) var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
  border-radius: var(--border-radius-small);
  transition: background-color 0.3s ease;
}

.layer-item:hover {
  background-color: var(--color-background);
}

.layer-item.active {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border: 1px solid var(--color-primary);
}

.layer-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.opacity-value {
  font-size: var(--font-size-xs);
  color: var(--color-text-soft);
  min-width: 30px;
}

.panel-footer {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  background-color: var(--color-background-mute);
}

.panel-footer .el-button {
  flex: 1;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: var(--color-background);
}

.panel-content::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}
</style>
