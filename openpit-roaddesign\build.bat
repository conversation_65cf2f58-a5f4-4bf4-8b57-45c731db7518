@echo off
chcp 65001 >nul
title 露天矿山道路设计软件构建器

echo.
echo ========================================
echo   露天矿山道路设计软件构建器 v1.0
echo ========================================
echo.

:: 设置生产环境变量
set NODE_ENV=production
set VITE_DEBUG_MODE=false
set VITE_LOG_LEVEL=error

echo 🏗️  生产构建配置:
echo    - 环境: 生产环境
echo    - 调试模式: 关闭
echo    - 日志级别: ERROR
echo    - 代码压缩: 启用
echo    - 源码映射: 关闭
echo.

:: 检查依赖
echo [1/7] 检查项目依赖...
if not exist "node_modules" (
    echo 📦 正在安装项目依赖...
    npm ci --production=false
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 依赖检查通过
)

:: 清理旧构建
echo [2/7] 清理旧构建文件...
if exist "dist" (
    rmdir /s /q "dist"
    echo ✅ 已清理dist目录
)
if exist ".vite" (
    rmdir /s /q ".vite"
    echo ✅ 已清理.vite缓存
)

:: 代码检查
echo [3/7] 执行代码质量检查...
echo    - ESLint检查...
npm run lint:eslint
if %errorlevel% neq 0 (
    echo ❌ ESLint检查失败，请修复代码问题后重试
    pause
    exit /b 1
)
echo ✅ ESLint检查通过

:: TypeScript类型检查
echo [4/7] 执行TypeScript类型检查...
npm run type-check
if %errorlevel% neq 0 (
    echo ❌ TypeScript类型检查失败，请修复类型错误后重试
    pause
    exit /b 1
)
echo ✅ TypeScript类型检查通过

:: 运行测试
echo [5/7] 执行单元测试...
npm run test:unit
if %errorlevel% neq 0 (
    echo ❌ 单元测试失败，请修复测试问题后重试
    pause
    exit /b 1
)
echo ✅ 单元测试通过

:: 构建项目
echo [6/7] 构建生产版本...
echo    这可能需要几分钟时间，请耐心等待...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)
echo ✅ 构建完成

:: 构建分析
echo [7/7] 生成构建分析报告...
if exist "dist" (
    echo 📊 构建统计:
    
    :: 计算文件大小
    for /f %%i in ('dir /s /b "dist\*.*" ^| find /c /v ""') do set FILE_COUNT=%%i
    echo    - 文件数量: %FILE_COUNT%
    
    :: 计算总大小
    for /f "tokens=3" %%i in ('dir /s "dist" ^| find "个文件"') do set TOTAL_SIZE=%%i
    echo    - 总大小: %TOTAL_SIZE% 字节
    
    :: 检查关键文件
    if exist "dist\index.html" echo    ✅ index.html
    if exist "dist\assets" echo    ✅ assets目录
    if exist "dist\cesium" echo    ✅ cesium资源
    
    echo.
    echo 🎉 构建成功完成！
    echo.
    echo 📁 构建输出目录: dist\
    echo 🌐 部署说明:
    echo    1. 将dist目录中的所有文件上传到Web服务器
    echo    2. 确保服务器支持单页应用(SPA)路由
    echo    3. 配置HTTPS以支持Cesium功能
    echo    4. 设置适当的缓存策略
    echo.
    echo 🚀 本地预览: npm run preview
    echo.
) else (
    echo ❌ 构建目录不存在，构建可能失败
)

pause
