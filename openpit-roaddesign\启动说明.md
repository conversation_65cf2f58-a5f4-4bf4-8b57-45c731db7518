# 露天矿山道路设计软件 - 启动说明

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

1. **双击运行启动脚本**
   ```
   启动软件.bat        # 完整版启动脚本
   简单启动.bat        # 简化版启动脚本
   调试启动.bat        # 调试模式启动脚本
   ```

2. **等待启动完成**
   - 脚本会自动检查环境
   - 自动安装依赖（首次运行）
   - 启动开发服务器

3. **打开浏览器访问**
   ```
   http://localhost:3000
   ```

### 方法二：手动启动

1. **打开命令行**
   - 按 `Win + R`，输入 `cmd`，回车
   - 或者在项目文件夹中按住 `Shift` 右键，选择"在此处打开命令窗口"

2. **切换到项目目录**
   ```bash
   cd "E:\python project\openpit-roaddesign-vue-a\openpit-roaddesign"
   ```

3. **安装依赖（首次运行）**
   ```bash
   npm install
   ```

4. **启动开发服务器**
   ```bash
   npm run dev
   ```

5. **打开浏览器访问**
   ```
   http://localhost:3000
   ```

## 🔧 环境要求

### 必需软件
- **Node.js**: 16.0+ (推荐18.0+)
  - 下载地址: https://nodejs.org/
  - 选择LTS版本
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Linux
- **内存**: 4GB+ (推荐8GB+)
- **硬盘**: 2GB+ 可用空间
- **显卡**: 支持WebGL 2.0

## 🐛 常见问题

### 1. Node.js未安装
**错误信息**: `'node' 不是内部或外部命令`

**解决方案**:
1. 访问 https://nodejs.org/
2. 下载并安装LTS版本
3. 重启命令行窗口
4. 验证安装: `node --version`

### 2. 端口被占用
**错误信息**: `Port 3000 is already in use`

**解决方案**:
1. 关闭占用端口的程序
2. 或者修改端口: `npm run dev -- --port 3001`

### 3. 依赖安装失败
**错误信息**: `npm install` 失败

**解决方案**:
1. 检查网络连接
2. 使用淘宝镜像:
   ```bash
   npm config set registry https://registry.npmmirror.com
   npm install
   ```
3. 清理缓存:
   ```bash
   npm cache clean --force
   npm install
   ```

### 4. 内存不足
**错误信息**: JavaScript heap out of memory

**解决方案**:
1. 关闭不必要的程序
2. 增加Node.js内存限制:
   ```bash
   set NODE_OPTIONS=--max-old-space-size=4096
   npm run dev
   ```

## 🔍 系统诊断

如果遇到问题，可以运行系统诊断脚本：

```
系统诊断.bat
```

诊断脚本会检查：
- 系统环境
- Node.js安装
- 项目文件
- 网络连接
- 磁盘空间

## 📞 技术支持

如果仍然无法启动，请：

1. **运行系统诊断脚本**
2. **保存诊断报告**
3. **联系技术支持**，并提供：
   - 诊断报告
   - 错误截图
   - 系统信息

## 🎯 开发模式功能

启动成功后，您可以使用以下功能：

### 主要模块
- **GIS数据管理** - 3D地球视图和数据可视化
- **数据管理系统** - 多类型数据导入和管理
- **道路设计** - 自动选线和路线优化
- **安全检测** - 智能安全评估和报告
- **报告输出** - 自动生成设计报告

### 调试工具
- **浏览器开发者工具** - F12打开
- **Vue DevTools** - 浏览器扩展
- **性能监控** - 内置性能监控面板
- **热重载** - 代码修改自动刷新

## 📝 更新日志

- **v1.0.0** - 初始版本发布
  - 完整的道路设计功能
  - 3D可视化支持
  - 安全检测系统
  - 性能优化

---

**露天矿山道路设计软件** - 专业、智能、高效的道路设计解决方案
