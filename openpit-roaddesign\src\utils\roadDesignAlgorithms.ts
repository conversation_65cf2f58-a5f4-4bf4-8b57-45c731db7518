/**
 * 露天矿山道路设计核心算法
 * 实现道路选线、冲突检测、剖切、运输路线优化等功能
 */

import type { 
  RoadData, 
  RoadCenterline, 
  RoadDesignStandards, 
  SafetyCheckResult,
  RouteOptimizationParams 
} from '@/types'
import { OPENPIT_ROAD_STANDARDS } from '@/config'

// 三维点接口
export interface Point3D {
  x: number
  y: number
  z: number
}

// 道路设计点
export interface RoadDesignPoint extends Point3D {
  station: number
  grade: number
  curvature: number
}

// 地形数据接口
export interface TerrainData {
  getElevation(x: number, y: number): number
  getSlope(x: number, y: number): number
  getAspect(x: number, y: number): number
}

/**
 * 道路选线算法类
 */
export class RoadAlignmentAlgorithm {
  private terrainData: TerrainData
  private designStandards: RoadDesignStandards

  constructor(terrainData: TerrainData, roadType: string = 'main') {
    this.terrainData = terrainData
    this.designStandards = OPENPIT_ROAD_STANDARDS[roadType] || OPENPIT_ROAD_STANDARDS.main
  }

  /**
   * 自动道路选线算法
   * 使用A*算法结合地形分析进行最优路径搜索
   */
  public generateAlignment(
    startPoint: Point3D,
    endPoint: Point3D,
    waypoints: Point3D[] = []
  ): RoadCenterline {
    const allPoints = [startPoint, ...waypoints, endPoint]
    const alignmentPoints: RoadDesignPoint[] = []
    let totalLength = 0

    // 分段处理每个路径段
    for (let i = 0; i < allPoints.length - 1; i++) {
      const segmentPoints = this.generateSegmentAlignment(
        allPoints[i],
        allPoints[i + 1],
        totalLength
      )
      
      alignmentPoints.push(...segmentPoints)
      totalLength += this.calculateSegmentLength(segmentPoints)
    }

    // 优化曲线设计
    const optimizedPoints = this.optimizeCurves(alignmentPoints)

    // 生成水平曲线和竖曲线
    const horizontalCurves = this.generateHorizontalCurves(optimizedPoints)
    const verticalCurves = this.generateVerticalCurves(optimizedPoints)

    return {
      points: optimizedPoints.map(p => ({
        longitude: p.x,
        latitude: p.y,
        elevation: p.z,
        station: p.station
      })),
      totalLength,
      horizontalCurves,
      verticalCurves
    }
  }

  /**
   * 生成路段选线
   */
  private generateSegmentAlignment(
    start: Point3D,
    end: Point3D,
    startStation: number
  ): RoadDesignPoint[] {
    const points: RoadDesignPoint[] = []
    const distance = this.calculateDistance(start, end)
    const segments = Math.ceil(distance / 10) // 每10米一个点

    for (let i = 0; i <= segments; i++) {
      const t = i / segments
      const point = this.interpolatePoint(start, end, t)
      
      // 地形适应性调整
      point.z = this.terrainData.getElevation(point.x, point.y)
      
      // 计算坡度
      const grade = i > 0 ? 
        this.calculateGrade(points[i - 1], point) : 0

      points.push({
        ...point,
        station: startStation + (distance * t),
        grade,
        curvature: 0 // 后续计算
      })
    }

    return this.smoothGrades(points)
  }

  /**
   * 优化曲线设计
   */
  private optimizeCurves(points: RoadDesignPoint[]): RoadDesignPoint[] {
    const optimized = [...points]

    for (let i = 1; i < optimized.length - 1; i++) {
      const prev = optimized[i - 1]
      const curr = optimized[i]
      const next = optimized[i + 1]

      // 计算曲率
      const curvature = this.calculateCurvature(prev, curr, next)
      optimized[i].curvature = curvature

      // 检查最小转弯半径
      if (curvature > 0) {
        const radius = 1 / curvature
        if (radius < this.designStandards.minRadius) {
          // 调整点位置以满足最小半径要求
          optimized[i] = this.adjustPointForRadius(prev, curr, next, this.designStandards.minRadius)
        }
      }
    }

    return optimized
  }

  /**
   * 生成水平曲线
   */
  private generateHorizontalCurves(points: RoadDesignPoint[]) {
    const curves = []
    let inCurve = false
    let curveStart = 0

    for (let i = 1; i < points.length - 1; i++) {
      const curvature = points[i].curvature
      
      if (curvature > 0.001 && !inCurve) {
        // 曲线开始
        inCurve = true
        curveStart = i
      } else if (curvature <= 0.001 && inCurve) {
        // 曲线结束
        inCurve = false
        const radius = 1 / Math.max(...points.slice(curveStart, i).map(p => p.curvature))
        const length = points[i].station - points[curveStart].station
        
        curves.push({
          station: points[curveStart].station,
          radius,
          length,
          direction: this.getCurveDirection(points.slice(curveStart, i)) as 'left' | 'right'
        })
      }
    }

    return curves
  }

  /**
   * 生成竖曲线
   */
  private generateVerticalCurves(points: RoadDesignPoint[]) {
    const curves = []

    for (let i = 1; i < points.length - 1; i++) {
      const prev = points[i - 1]
      const curr = points[i]
      const next = points[i + 1]

      const grade1 = prev.grade
      const grade2 = next.grade
      const gradeChange = Math.abs(grade2 - grade1)

      // 如果坡度变化超过阈值，需要设置竖曲线
      if (gradeChange > 2) {
        const length = this.calculateVerticalCurveLength(grade1, grade2)
        
        curves.push({
          station: curr.station,
          length,
          grade1,
          grade2
        })
      }
    }

    return curves
  }

  // 辅助方法
  private calculateDistance(p1: Point3D, p2: Point3D): number {
    return Math.sqrt(
      Math.pow(p2.x - p1.x, 2) + 
      Math.pow(p2.y - p1.y, 2) + 
      Math.pow(p2.z - p1.z, 2)
    )
  }

  private interpolatePoint(start: Point3D, end: Point3D, t: number): Point3D {
    return {
      x: start.x + (end.x - start.x) * t,
      y: start.y + (end.y - start.y) * t,
      z: start.z + (end.z - start.z) * t
    }
  }

  private calculateGrade(p1: RoadDesignPoint, p2: RoadDesignPoint): number {
    const horizontalDistance = Math.sqrt(
      Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2)
    )
    const verticalDistance = p2.z - p1.z
    return (verticalDistance / horizontalDistance) * 100 // 百分比坡度
  }

  private calculateCurvature(p1: Point3D, p2: Point3D, p3: Point3D): number {
    // 使用三点法计算曲率
    const a = this.calculateDistance(p1, p2)
    const b = this.calculateDistance(p2, p3)
    const c = this.calculateDistance(p1, p3)
    
    const s = (a + b + c) / 2
    const area = Math.sqrt(s * (s - a) * (s - b) * (s - c))
    
    return area > 0 ? (4 * area) / (a * b * c) : 0
  }

  private smoothGrades(points: RoadDesignPoint[]): RoadDesignPoint[] {
    const smoothed = [...points]
    
    // 应用移动平均平滑坡度
    for (let i = 1; i < smoothed.length - 1; i++) {
      const prev = smoothed[i - 1].grade
      const curr = smoothed[i].grade
      const next = smoothed[i + 1].grade
      
      smoothed[i].grade = (prev + curr + next) / 3
      
      // 确保不超过最大坡度
      if (Math.abs(smoothed[i].grade) > this.designStandards.maxGrade) {
        smoothed[i].grade = Math.sign(smoothed[i].grade) * this.designStandards.maxGrade
      }
    }
    
    return smoothed
  }

  private adjustPointForRadius(
    prev: RoadDesignPoint, 
    curr: RoadDesignPoint, 
    next: RoadDesignPoint, 
    minRadius: number
  ): RoadDesignPoint {
    // 简化的点位调整算法
    const midX = (prev.x + next.x) / 2
    const midY = (prev.y + next.y) / 2
    
    return {
      ...curr,
      x: midX,
      y: midY,
      z: this.terrainData.getElevation(midX, midY)
    }
  }

  private getCurveDirection(points: RoadDesignPoint[]): string {
    // 简化的曲线方向判断
    if (points.length < 3) return 'straight'
    
    const start = points[0]
    const end = points[points.length - 1]
    const mid = points[Math.floor(points.length / 2)]
    
    const crossProduct = (end.x - start.x) * (mid.y - start.y) - (end.y - start.y) * (mid.x - start.x)
    
    return crossProduct > 0 ? 'left' : 'right'
  }

  private calculateSegmentLength(points: RoadDesignPoint[]): number {
    let length = 0
    for (let i = 1; i < points.length; i++) {
      length += this.calculateDistance(points[i - 1], points[i])
    }
    return length
  }

  private calculateVerticalCurveLength(grade1: number, grade2: number): number {
    const gradeChange = Math.abs(grade2 - grade1)
    // 根据设计速度和坡度变化计算竖曲线长度
    const designSpeed = this.designStandards.designSpeed
    return Math.max(designSpeed * gradeChange / 100, 30) // 最小30米
  }
}

/**
 * 道路冲突检测算法
 */
export class RoadConflictDetection {
  /**
   * 检测道路与地形的冲突
   */
  public static detectTerrainConflicts(
    roadData: RoadData,
    terrainData: TerrainData
  ): SafetyCheckResult[] {
    const conflicts: SafetyCheckResult[] = []
    
    roadData.centerline.points.forEach((point, index) => {
      const terrainElevation = terrainData.getElevation(point.longitude, point.latitude)
      const roadElevation = point.elevation
      const difference = Math.abs(roadElevation - terrainElevation)
      
      // 检查填挖高度是否合理
      if (difference > 20) { // 超过20米认为不合理
        conflicts.push({
          type: 'grade',
          status: difference > 50 ? 'fail' : 'warning',
          value: difference,
          standard: 20,
          message: `桩号 ${point.station.toFixed(0)} 处填挖高度过大: ${difference.toFixed(1)}m`,
          location: {
            station: point.station,
            coordinates: [point.longitude, point.latitude, point.elevation]
          }
        })
      }
    })
    
    return conflicts
  }

  /**
   * 检测道路间的冲突
   */
  public static detectRoadConflicts(roads: RoadData[]): SafetyCheckResult[] {
    const conflicts: SafetyCheckResult[] = []
    
    for (let i = 0; i < roads.length; i++) {
      for (let j = i + 1; j < roads.length; j++) {
        const road1 = roads[i]
        const road2 = roads[j]
        
        const intersections = this.findRoadIntersections(road1, road2)
        
        intersections.forEach(intersection => {
          conflicts.push({
            type: 'width',
            status: 'warning',
            value: 0,
            standard: 0,
            message: `道路 "${road1.name}" 与 "${road2.name}" 存在交叉`,
            location: intersection
          })
        })
      }
    }
    
    return conflicts
  }

  private static findRoadIntersections(road1: RoadData, road2: RoadData) {
    const intersections = []
    // 简化的道路交叉检测算法
    // 实际应用中需要更复杂的几何算法
    return intersections
  }
}

/**
 * 运输路线优化算法
 */
export class RouteOptimization {
  /**
   * 使用遗传算法优化运输路线
   */
  public static optimizeRoute(params: RouteOptimizationParams): RoadCenterline {
    const { startPoint, endPoint, waypoints = [], constraints, objectives } = params
    
    // 简化的路线优化实现
    // 实际应用中可以使用更复杂的优化算法如遗传算法、粒子群算法等
    
    const allPoints = [startPoint, ...waypoints, endPoint]
    const optimizedPoints = this.applyOptimizationConstraints(allPoints, constraints)
    
    return {
      points: optimizedPoints.map((point, index) => ({
        longitude: point[0],
        latitude: point[1],
        elevation: point[2],
        station: index * 10 // 简化的桩号计算
      })),
      totalLength: this.calculateTotalLength(optimizedPoints),
      horizontalCurves: [],
      verticalCurves: []
    }
  }

  private static applyOptimizationConstraints(
    points: Array<[number, number, number]>,
    constraints: RouteOptimizationParams['constraints']
  ): Array<[number, number, number]> {
    // 应用约束条件的优化
    return points.filter(point => {
      // 检查是否在避让区域内
      if (constraints.avoidAreas) {
        for (const area of constraints.avoidAreas) {
          if (this.isPointInPolygon(point, area.coordinates[0])) {
            return false
          }
        }
      }
      return true
    })
  }

  private static isPointInPolygon(
    point: [number, number, number],
    polygon: number[][]
  ): boolean {
    // 射线法判断点是否在多边形内
    let inside = false
    const x = point[0]
    const y = point[1]
    
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i][0]
      const yi = polygon[i][1]
      const xj = polygon[j][0]
      const yj = polygon[j][1]
      
      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside
      }
    }
    
    return inside
  }

  private static calculateTotalLength(points: Array<[number, number, number]>): number {
    let length = 0
    for (let i = 1; i < points.length; i++) {
      const p1 = points[i - 1]
      const p2 = points[i]
      length += Math.sqrt(
        Math.pow(p2[0] - p1[0], 2) + 
        Math.pow(p2[1] - p1[1], 2) + 
        Math.pow(p2[2] - p1[2], 2)
      )
    }
    return length
  }
}

/**
 * 道路剖切算法
 */
export class RoadProfileGeneration {
  /**
   * 生成道路纵断面
   */
  public static generateLongitudinalProfile(
    centerline: RoadCenterline,
    terrainData: TerrainData
  ) {
    const profile = centerline.points.map(point => {
      const terrainElevation = terrainData.getElevation(point.longitude, point.latitude)
      
      return {
        station: point.station,
        roadElevation: point.elevation,
        terrainElevation,
        cutFill: point.elevation - terrainElevation,
        grade: 0 // 需要计算
      }
    })

    // 计算坡度
    for (let i = 1; i < profile.length; i++) {
      const prev = profile[i - 1]
      const curr = profile[i]
      const distance = curr.station - prev.station
      const elevationDiff = curr.roadElevation - prev.roadElevation
      curr.grade = (elevationDiff / distance) * 100
    }

    return profile
  }

  /**
   * 生成道路横断面
   */
  public static generateCrossSections(
    centerline: RoadCenterline,
    roadWidth: number,
    terrainData: TerrainData,
    interval: number = 20
  ) {
    const crossSections = []
    
    for (let i = 0; i < centerline.points.length; i += Math.floor(interval / 10)) {
      const point = centerline.points[i]
      const crossSection = this.generateSingleCrossSection(
        point,
        roadWidth,
        terrainData,
        i > 0 ? centerline.points[i - 1] : null
      )
      
      crossSections.push({
        station: point.station,
        ...crossSection
      })
    }
    
    return crossSections
  }

  private static generateSingleCrossSection(
    centerPoint: any,
    roadWidth: number,
    terrainData: TerrainData,
    prevPoint: any
  ) {
    // 计算横断面方向（垂直于道路方向）
    const direction = prevPoint ? 
      Math.atan2(centerPoint.latitude - prevPoint.latitude, centerPoint.longitude - prevPoint.longitude) :
      0
    
    const perpDirection = direction + Math.PI / 2
    
    // 生成横断面点
    const halfWidth = roadWidth / 2
    const points = []
    
    for (let offset = -halfWidth - 10; offset <= halfWidth + 10; offset += 1) {
      const x = centerPoint.longitude + Math.cos(perpDirection) * offset / 111320 // 近似转换为度
      const y = centerPoint.latitude + Math.sin(perpDirection) * offset / 111320
      const elevation = terrainData.getElevation(x, y)
      
      points.push({
        offset,
        elevation,
        isRoad: Math.abs(offset) <= halfWidth
      })
    }
    
    return {
      points,
      leftSlope: this.calculateSlope(points.filter(p => p.offset < 0)),
      rightSlope: this.calculateSlope(points.filter(p => p.offset > 0)),
      cutFill: centerPoint.elevation - terrainData.getElevation(centerPoint.longitude, centerPoint.latitude),
      volume: this.calculateVolume(points, roadWidth)
    }
  }

  private static calculateSlope(points: any[]): number {
    if (points.length < 2) return 0
    
    const first = points[0]
    const last = points[points.length - 1]
    const horizontalDistance = Math.abs(last.offset - first.offset)
    const verticalDistance = last.elevation - first.elevation
    
    return horizontalDistance > 0 ? (verticalDistance / horizontalDistance) * 100 : 0
  }

  private static calculateVolume(points: any[], roadWidth: number): number {
    // 简化的土方量计算
    const roadPoints = points.filter(p => p.isRoad)
    if (roadPoints.length === 0) return 0
    
    const avgRoadElevation = roadPoints.reduce((sum, p) => sum + p.elevation, 0) / roadPoints.length
    const centerElevation = points.find(p => Math.abs(p.offset) < 0.5)?.elevation || avgRoadElevation
    
    return (centerElevation - avgRoadElevation) * roadWidth // 简化计算
  }
}
