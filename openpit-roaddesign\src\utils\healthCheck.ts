/**
 * 系统健康检查工具
 * 检查系统各个组件的运行状态
 */

import { logger } from './logger'
import { performanceOptimizer } from './performanceOptimizer'
import { dataFlowManager } from './dataFlowManager'

export interface HealthCheckResult {
  component: string
  status: 'healthy' | 'warning' | 'error'
  message: string
  details?: any
  timestamp: Date
}

export interface SystemHealth {
  overall: 'healthy' | 'warning' | 'error'
  score: number
  checks: HealthCheckResult[]
  timestamp: Date
}

class HealthChecker {
  private static instance: HealthChecker
  private checkInterval: number | null = null

  private constructor() {}

  public static getInstance(): HealthChecker {
    if (!HealthChecker.instance) {
      HealthChecker.instance = new HealthChecker()
    }
    return HealthChecker.instance
  }

  /**
   * 执行完整的系统健康检查
   */
  public async performHealthCheck(): Promise<SystemHealth> {
    const checks: HealthCheckResult[] = []
    
    // 检查各个组件
    checks.push(await this.checkMemory())
    checks.push(await this.checkPerformance())
    checks.push(await this.checkDataFlow())
    checks.push(await this.checkBrowser())
    checks.push(await this.checkNetwork())
    checks.push(await this.checkStorage())
    checks.push(await this.checkCesium())
    
    // 计算总体健康状态
    const healthyCount = checks.filter(c => c.status === 'healthy').length
    const warningCount = checks.filter(c => c.status === 'warning').length
    const errorCount = checks.filter(c => c.status === 'error').length
    
    let overall: 'healthy' | 'warning' | 'error' = 'healthy'
    if (errorCount > 0) {
      overall = 'error'
    } else if (warningCount > 0) {
      overall = 'warning'
    }
    
    const score = Math.round((healthyCount * 100 + warningCount * 60) / checks.length)
    
    const systemHealth: SystemHealth = {
      overall,
      score,
      checks,
      timestamp: new Date()
    }
    
    logger.info('系统健康检查完成', 'HealthCheck', {
      overall,
      score,
      totalChecks: checks.length,
      healthy: healthyCount,
      warning: warningCount,
      error: errorCount
    })
    
    return systemHealth
  }

  /**
   * 检查内存使用情况
   */
  private async checkMemory(): Promise<HealthCheckResult> {
    try {
      const metrics = performanceOptimizer.getMetrics()
      const memoryUsage = metrics.memoryUsage.percentage
      
      let status: 'healthy' | 'warning' | 'error' = 'healthy'
      let message = `内存使用率 ${memoryUsage.toFixed(1)}%`
      
      if (memoryUsage > 90) {
        status = 'error'
        message += ' - 内存使用率过高'
      } else if (memoryUsage > 75) {
        status = 'warning'
        message += ' - 内存使用率较高'
      } else {
        message += ' - 正常'
      }
      
      return {
        component: 'Memory',
        status,
        message,
        details: {
          used: metrics.memoryUsage.used,
          total: metrics.memoryUsage.total,
          percentage: memoryUsage
        },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        component: 'Memory',
        status: 'error',
        message: '内存检查失败',
        details: { error: (error as Error).message },
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查性能指标
   */
  private async checkPerformance(): Promise<HealthCheckResult> {
    try {
      const metrics = performanceOptimizer.getMetrics()
      const fps = metrics.fps
      const renderTime = metrics.renderTime
      
      let status: 'healthy' | 'warning' | 'error' = 'healthy'
      let message = `FPS: ${fps}, 渲染时间: ${renderTime.toFixed(2)}ms`
      
      if (fps < 20 || renderTime > 50) {
        status = 'error'
        message += ' - 性能严重不足'
      } else if (fps < 30 || renderTime > 30) {
        status = 'warning'
        message += ' - 性能需要优化'
      } else {
        message += ' - 性能良好'
      }
      
      return {
        component: 'Performance',
        status,
        message,
        details: {
          fps,
          renderTime,
          computeTime: metrics.computeTime
        },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        component: 'Performance',
        status: 'error',
        message: '性能检查失败',
        details: { error: (error as Error).message },
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查数据流管理器
   */
  private async checkDataFlow(): Promise<HealthCheckResult> {
    try {
      const eventHistory = dataFlowManager.getEventHistory(10)
      const recentEvents = eventHistory.filter(e => 
        Date.now() - e.timestamp < 60000 // 最近1分钟的事件
      )
      
      let status: 'healthy' | 'warning' | 'error' = 'healthy'
      let message = `数据流正常，最近1分钟有 ${recentEvents.length} 个事件`
      
      if (recentEvents.length === 0 && eventHistory.length > 0) {
        status = 'warning'
        message = '数据流活动较少'
      }
      
      return {
        component: 'DataFlow',
        status,
        message,
        details: {
          totalEvents: eventHistory.length,
          recentEvents: recentEvents.length
        },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        component: 'DataFlow',
        status: 'error',
        message: '数据流检查失败',
        details: { error: (error as Error).message },
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查浏览器兼容性
   */
  private async checkBrowser(): Promise<HealthCheckResult> {
    try {
      const features = {
        webgl: !!window.WebGLRenderingContext,
        webgl2: !!window.WebGL2RenderingContext,
        webWorker: typeof Worker !== 'undefined',
        localStorage: typeof Storage !== 'undefined',
        indexedDB: 'indexedDB' in window,
        geolocation: 'geolocation' in navigator
      }
      
      const supportedFeatures = Object.values(features).filter(Boolean).length
      const totalFeatures = Object.keys(features).length
      
      let status: 'healthy' | 'warning' | 'error' = 'healthy'
      let message = `浏览器兼容性良好 (${supportedFeatures}/${totalFeatures})`
      
      if (!features.webgl) {
        status = 'error'
        message = '浏览器不支持WebGL，无法运行3D功能'
      } else if (supportedFeatures < totalFeatures * 0.8) {
        status = 'warning'
        message = `浏览器兼容性一般 (${supportedFeatures}/${totalFeatures})`
      }
      
      return {
        component: 'Browser',
        status,
        message,
        details: features,
        timestamp: new Date()
      }
    } catch (error) {
      return {
        component: 'Browser',
        status: 'error',
        message: '浏览器检查失败',
        details: { error: (error as Error).message },
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查网络连接
   */
  private async checkNetwork(): Promise<HealthCheckResult> {
    try {
      const startTime = performance.now()
      
      // 尝试获取一个小的资源来测试网络
      const response = await fetch('/favicon.ico', { 
        method: 'HEAD',
        cache: 'no-cache'
      }).catch(() => null)
      
      const endTime = performance.now()
      const latency = endTime - startTime
      
      let status: 'healthy' | 'warning' | 'error' = 'healthy'
      let message = `网络延迟 ${latency.toFixed(0)}ms`
      
      if (!response) {
        status = 'error'
        message = '网络连接失败'
      } else if (latency > 2000) {
        status = 'error'
        message += ' - 网络延迟过高'
      } else if (latency > 1000) {
        status = 'warning'
        message += ' - 网络延迟较高'
      } else {
        message += ' - 网络连接良好'
      }
      
      return {
        component: 'Network',
        status,
        message,
        details: {
          latency,
          online: navigator.onLine,
          connection: (navigator as any).connection
        },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        component: 'Network',
        status: 'error',
        message: '网络检查失败',
        details: { error: (error as Error).message },
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查本地存储
   */
  private async checkStorage(): Promise<HealthCheckResult> {
    try {
      const testKey = '__health_check_test__'
      const testValue = 'test'
      
      // 测试localStorage
      localStorage.setItem(testKey, testValue)
      const retrieved = localStorage.getItem(testKey)
      localStorage.removeItem(testKey)
      
      let status: 'healthy' | 'warning' | 'error' = 'healthy'
      let message = '本地存储功能正常'
      
      if (retrieved !== testValue) {
        status = 'error'
        message = '本地存储功能异常'
      }
      
      // 检查存储空间
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate()
        const usedMB = (estimate.usage || 0) / 1024 / 1024
        const quotaMB = (estimate.quota || 0) / 1024 / 1024
        const usagePercent = quotaMB > 0 ? (usedMB / quotaMB) * 100 : 0
        
        if (usagePercent > 90) {
          status = 'warning'
          message += ` - 存储空间使用率 ${usagePercent.toFixed(1)}%`
        }
      }
      
      return {
        component: 'Storage',
        status,
        message,
        details: {
          localStorage: retrieved === testValue,
          estimate: 'storage' in navigator ? await navigator.storage.estimate() : null
        },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        component: 'Storage',
        status: 'error',
        message: '存储检查失败',
        details: { error: (error as Error).message },
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查Cesium状态
   */
  private async checkCesium(): Promise<HealthCheckResult> {
    try {
      // 检查Cesium是否已加载
      const cesiumLoaded = typeof window.Cesium !== 'undefined'
      
      let status: 'healthy' | 'warning' | 'error' = 'healthy'
      let message = 'Cesium引擎正常'
      
      if (!cesiumLoaded) {
        status = 'warning'
        message = 'Cesium引擎未加载'
      }
      
      return {
        component: 'Cesium',
        status,
        message,
        details: {
          loaded: cesiumLoaded,
          version: cesiumLoaded ? (window as any).Cesium?.VERSION : null
        },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        component: 'Cesium',
        status: 'error',
        message: 'Cesium检查失败',
        details: { error: (error as Error).message },
        timestamp: new Date()
      }
    }
  }

  /**
   * 启动定期健康检查
   */
  public startPeriodicCheck(intervalMs: number = 60000) {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }
    
    this.checkInterval = window.setInterval(async () => {
      const health = await this.performHealthCheck()
      
      if (health.overall === 'error') {
        logger.error('系统健康检查发现严重问题', 'HealthCheck', health)
      } else if (health.overall === 'warning') {
        logger.warn('系统健康检查发现警告', 'HealthCheck', health)
      }
    }, intervalMs)
    
    logger.info(`已启动定期健康检查，间隔 ${intervalMs}ms`, 'HealthCheck')
  }

  /**
   * 停止定期健康检查
   */
  public stopPeriodicCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
      logger.info('已停止定期健康检查', 'HealthCheck')
    }
  }
}

// 导出单例实例
export const healthChecker = HealthChecker.getInstance()

// 便捷函数
export async function checkSystemHealth(): Promise<SystemHealth> {
  return await healthChecker.performHealthCheck()
}

export function startHealthMonitoring(intervalMs?: number) {
  healthChecker.startPeriodicCheck(intervalMs)
}

export function stopHealthMonitoring() {
  healthChecker.stopPeriodicCheck()
}
