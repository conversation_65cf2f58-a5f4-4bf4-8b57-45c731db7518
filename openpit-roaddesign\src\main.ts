import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'

// 导入数据流管理器和模块集成服务
import { dataFlowManager } from '@/utils/dataFlowManager'
import { moduleIntegrationService } from '@/services/moduleIntegrationService'

const app = createApp(App)

// 注册所有 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 初始化数据流管理和模块集成
console.log('初始化数据流管理器和模块集成服务...')
console.log('数据流管理器状态:', dataFlowManager.getEventHistory().length)
console.log('模块集成服务状态:', moduleIntegrationService.getIntegrationState())

app.mount('#app')
