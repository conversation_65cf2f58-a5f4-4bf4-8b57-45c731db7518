import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'

// 导入核心服务
import { dataFlowManager } from '@/utils/dataFlowManager'
import { moduleIntegrationService } from '@/services/moduleIntegrationService'
import { logger, setupGlobalErrorHandling } from '@/utils/logger'
import { healthChecker, startHealthMonitoring } from '@/utils/healthCheck'
import { performanceOptimizer } from '@/utils/performanceOptimizer'

// 应用信息
const APP_VERSION = import.meta.env.VITE_APP_VERSION || '1.0.0'
const BUILD_TIME = (globalThis as any).__BUILD_TIME__ || new Date().toISOString()
const DEBUG_MODE = import.meta.env.VITE_DEBUG_MODE === 'true'

// 启动日志
logger.info('应用启动', 'Main', {
  version: APP_VERSION,
  buildTime: BUILD_TIME,
  debugMode: DEBUG_MODE,
  environment: import.meta.env.MODE
})

// 设置全局错误处理
setupGlobalErrorHandling()

// 创建Vue应用
const app = createApp(App)

// 存储应用实例供全局错误处理使用
;(window as any).__VUE_APP__ = app

// 注册所有 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus)

// 初始化核心服务
async function initializeServices() {
  try {
    logger.info('初始化核心服务...', 'Main')

    // 初始化数据流管理器
    logger.info('初始化数据流管理器', 'Main')
    const eventHistory = dataFlowManager.getEventHistory()
    logger.debug('数据流管理器状态', 'Main', { eventCount: eventHistory.length })

    // 初始化模块集成服务
    logger.info('初始化模块集成服务', 'Main')
    const integrationState = moduleIntegrationService.getIntegrationState()
    logger.debug('模块集成服务状态', 'Main', integrationState)

    // 初始化性能优化器
    logger.info('初始化性能优化器', 'Main')
    const metrics = performanceOptimizer.getMetrics()
    logger.debug('性能优化器状态', 'Main', metrics)

    // 执行系统健康检查
    logger.info('执行系统健康检查', 'Main')
    const healthStatus = await healthChecker.performHealthCheck()
    logger.info('系统健康检查完成', 'Main', {
      overall: healthStatus.overall,
      score: healthStatus.score
    })

    // 如果启用了健康监控，开始定期检查
    if (import.meta.env.VITE_SHOW_PERFORMANCE_MONITOR === 'true') {
      startHealthMonitoring(60000) // 每分钟检查一次
      logger.info('已启动系统健康监控', 'Main')
    }

    logger.info('所有核心服务初始化完成', 'Main')

  } catch (error) {
    logger.error('核心服务初始化失败', 'Main', {}, error as Error)
    throw error
  }
}

// 挂载应用
async function mountApp() {
  try {
    // 初始化服务
    await initializeServices()

    // 挂载应用
    app.mount('#app')

    logger.info('应用挂载成功', 'Main', {
      mountTime: new Date().toISOString(),
      version: APP_VERSION
    })

    // 在开发模式下显示调试信息
    if (DEBUG_MODE) {
      console.group('🚀 露天矿山道路设计软件')
      console.log('版本:', APP_VERSION)
      console.log('构建时间:', BUILD_TIME)
      console.log('运行模式:', import.meta.env.MODE)
      console.log('调试模式:', DEBUG_MODE)
      console.groupEnd()
    }

  } catch (error) {
    logger.error('应用启动失败', 'Main', {}, error as Error)

    // 显示错误信息给用户
    const errorDiv = document.createElement('div')
    errorDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #f56565;
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        z-index: 9999;
        font-family: Arial, sans-serif;
      ">
        <h3>应用启动失败</h3>
        <p>请检查浏览器控制台获取详细错误信息</p>
        <p>或联系技术支持</p>
        <button onclick="location.reload()" style="
          background: white;
          color: #f56565;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
          margin-top: 10px;
        ">重新加载</button>
      </div>
    `
    document.body.appendChild(errorDiv)
  }
}

// 启动应用
mountApp()
