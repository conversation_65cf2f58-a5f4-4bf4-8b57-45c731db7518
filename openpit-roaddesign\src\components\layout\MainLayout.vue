<template>
  <div class="main-layout">
    <!-- 顶部标题栏 -->
    <header class="header">
      <div class="header-left">
        <h1 class="app-title">露天矿山道路设计系统</h1>
      </div>
      <div class="header-center">
        <el-menu
          :default-active="activeMenu"
          class="header-menu"
          mode="horizontal"
          background-color="#2c2c2c"
          text-color="#ffffff"
          active-text-color="#ffd04b"
          @select="handleMenuSelect"
        >
          <el-menu-item index="gis">GIS数据管理</el-menu-item>
          <el-menu-item index="road-design">道路设计</el-menu-item>
          <el-menu-item index="road-analysis">道路分析</el-menu-item>
          <el-menu-item index="safety">安全检测</el-menu-item>
          <el-menu-item index="data-import">数据导入导出</el-menu-item>
        </el-menu>
      </div>
      <div class="header-right">
        <el-button type="primary" size="small">
          <el-icon><Setting /></el-icon>
          设置
        </el-button>
      </div>
    </header>

    <div class="main-content">
      <!-- 左侧数据操作区 -->
      <aside class="sidebar">
        <div class="sidebar-header">
          <h3>{{ sidebarTitle }}</h3>
        </div>
        <div class="sidebar-content">
          <slot name="sidebar">
            <div class="default-sidebar">
              <p>请选择功能模块</p>
            </div>
          </slot>
        </div>
      </aside>

      <!-- 中间工作区 -->
      <main class="workspace">
        <div class="workspace-header">
          <div class="workspace-title">
            <h2>{{ workspaceTitle }}</h2>
          </div>
          <div class="workspace-tools">
            <slot name="workspace-tools"></slot>
          </div>
        </div>
        <div class="workspace-content">
          <slot name="workspace">
            <div class="default-workspace">
              <div class="cesium-container" ref="cesiumContainer"></div>
            </div>
          </slot>
        </div>
      </main>

      <!-- 右侧悬浮按钮区 -->
      <div class="floating-tools">
        <el-button-group direction="vertical">
          <el-tooltip content="视图控制" placement="left">
            <el-button type="primary" :icon="View" circle @click="handleViewControl" />
          </el-tooltip>
          <el-tooltip content="测量工具" placement="left">
            <el-button type="success" :icon="Ruler" circle @click="handleMeasure" />
          </el-tooltip>
          <el-tooltip content="图层管理" placement="left">
            <el-button type="info" :icon="Grid" circle @click="handleLayerManage" />
          </el-tooltip>
          <el-tooltip content="帮助" placement="left">
            <el-button type="warning" :icon="QuestionFilled" circle @click="handleHelp" />
          </el-tooltip>
        </el-button-group>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { View, Ruler, Grid, QuestionFilled, Setting } from '@element-plus/icons-vue'
import * as Cesium from 'cesium'

// Props
interface Props {
  sidebarTitle?: string
  workspaceTitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  sidebarTitle: '数据操作',
  workspaceTitle: '工作区'
})

// Reactive data
const activeMenu = ref('gis')
const cesiumContainer = ref<HTMLElement>()
let viewer: Cesium.Viewer | null = null

// Methods
const handleMenuSelect = (key: string) => {
  activeMenu.value = key
  // Emit event to parent component
  emit('menu-change', key)
}

const handleViewControl = () => {
  emit('view-control')
}

const handleMeasure = () => {
  emit('measure')
}

const handleLayerManage = () => {
  emit('layer-manage')
}

const handleHelp = () => {
  emit('help')
}

// Initialize Cesium viewer
const initCesium = () => {
  if (cesiumContainer.value) {
    viewer = new Cesium.Viewer(cesiumContainer.value, {
      terrainProvider: Cesium.createWorldTerrain(),
      timeline: false,
      animation: false,
      homeButton: false,
      sceneModePicker: false,
      baseLayerPicker: false,
      navigationHelpButton: false,
      fullscreenButton: false,
      vrButton: false,
      geocoder: false,
      infoBox: false,
      selectionIndicator: false
    })

    // Set initial view to China
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(116.4, 39.9, 15000000.0)
    })
  }
}

// Emits
const emit = defineEmits<{
  'menu-change': [key: string]
  'view-control': []
  'measure': []
  'layer-manage': []
  'help': []
}>()

// Lifecycle
onMounted(() => {
  initCesium()
})

onUnmounted(() => {
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})
</script>

<style scoped>
.main-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: #ffffff;
}

.header {
  height: 60px;
  background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
  border-bottom: 2px solid #ffd04b;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.header-left {
  flex: 0 0 300px;
}

.app-title {
  color: #ffd04b;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-menu {
  border: none;
}

.header-right {
  flex: 0 0 200px;
  display: flex;
  justify-content: flex-end;
}

.main-content {
  flex: 1;
  display: flex;
  position: relative;
}

.sidebar {
  width: 300px;
  background-color: #2c2c2c;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  height: 50px;
  background-color: #404040;
  display: flex;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid #555555;
}

.sidebar-header h3 {
  color: #ffd04b;
  margin: 0;
  font-size: 16px;
}

.sidebar-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.workspace {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
}

.workspace-header {
  height: 50px;
  background-color: #2c2c2c;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid #404040;
}

.workspace-title h2 {
  color: #ffffff;
  margin: 0;
  font-size: 18px;
}

.workspace-content {
  flex: 1;
  position: relative;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

.floating-tools {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
}

.floating-tools .el-button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.default-sidebar,
.default-workspace {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888888;
}
</style>
