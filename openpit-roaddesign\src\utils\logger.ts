/**
 * 简化的日志系统
 * 提供基本的日志记录功能
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface LogEntry {
  level: LogLevel
  message: string
  timestamp: Date
  module?: string
  data?: any
  stack?: string
}

class Logger {
  private static instance: Logger
  private logs: LogEntry[] = []
  private maxLogs = 1000
  private logLevel: LogLevel = LogLevel.INFO

  private constructor() {
    // 根据环境变量设置日志级别
    try {
      const envLogLevel = import.meta.env?.VITE_LOG_LEVEL
      switch (envLogLevel) {
        case 'debug':
          this.logLevel = LogLevel.DEBUG
          break
        case 'info':
          this.logLevel = LogLevel.INFO
          break
        case 'warn':
          this.logLevel = LogLevel.WARN
          break
        case 'error':
          this.logLevel = LogLevel.ERROR
          break
        default:
          this.logLevel = import.meta.env?.DEV ? LogLevel.DEBUG : LogLevel.ERROR
      }
    } catch (error) {
      this.logLevel = LogLevel.INFO
    }
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  private log(level: LogLevel, message: string, module?: string, data?: any, error?: Error) {
    if (level < this.logLevel) return

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      module,
      data,
      stack: error?.stack
    }

    this.logs.push(entry)
    
    // 限制日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs.shift()
    }

    // 控制台输出
    this.consoleOutput(entry, error)
  }

  private consoleOutput(entry: LogEntry, error?: Error) {
    const prefix = `[${entry.timestamp.toISOString()}] ${entry.module ? `[${entry.module}]` : ''}`
    const message = `${prefix} ${entry.message}`

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message, entry.data)
        break
      case LogLevel.INFO:
        console.info(message, entry.data)
        break
      case LogLevel.WARN:
        console.warn(message, entry.data)
        break
      case LogLevel.ERROR:
        console.error(message, entry.data, error)
        break
    }
  }

  public debug(message: string, module?: string, data?: any) {
    this.log(LogLevel.DEBUG, message, module, data)
  }

  public info(message: string, module?: string, data?: any) {
    this.log(LogLevel.INFO, message, module, data)
  }

  public warn(message: string, module?: string, data?: any) {
    this.log(LogLevel.WARN, message, module, data)
  }

  public error(message: string, module?: string, data?: any, error?: Error) {
    this.log(LogLevel.ERROR, message, module, data, error)
  }

  public getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level)
    }
    return [...this.logs]
  }

  public clearLogs() {
    this.logs = []
  }

  public setLogLevel(level: LogLevel) {
    this.logLevel = level
  }

  public exportLogs(): string {
    return this.logs.map(log => {
      const levelName = LogLevel[log.level]
      const timestamp = log.timestamp.toISOString()
      const module = log.module ? `[${log.module}]` : ''
      const data = log.data ? ` | Data: ${JSON.stringify(log.data)}` : ''
      const stack = log.stack ? `\nStack: ${log.stack}` : ''
      
      return `${timestamp} ${levelName} ${module} ${log.message}${data}${stack}`
    }).join('\n')
  }
}

// 导出单例实例
export const logger = Logger.getInstance()

// 全局错误处理
export function setupGlobalErrorHandling() {
  // 捕获未处理的Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    logger.error('未处理的Promise拒绝', 'Global', {
      reason: event.reason,
      promise: event.promise
    })
    
    // 阻止默认的控制台错误输出
    event.preventDefault()
  })

  // 捕获全局错误
  window.addEventListener('error', (event) => {
    logger.error('全局错误', 'Global', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    }, event.error)
  })

  // Vue错误处理
  const app = (window as any).__VUE_APP__
  if (app) {
    app.config.errorHandler = (error: Error, instance: any, info: string) => {
      logger.error('Vue错误', 'Vue', {
        info,
        componentName: instance?.$options?.name || 'Unknown',
        error: error.message
      }, error)
    }
  }
}

// 性能监控
export function logPerformance(name: string, startTime: number, module?: string, data?: any) {
  const duration = performance.now() - startTime
  logger.info(`性能监控: ${name} 耗时 ${duration.toFixed(2)}ms`, module, data)
  
  // 如果耗时过长，记录警告
  if (duration > 1000) {
    logger.warn(`性能警告: ${name} 耗时过长 ${duration.toFixed(2)}ms`, module, data)
  }
}

// 便捷函数
export function logDebug(message: string, module?: string, data?: any) {
  logger.debug(message, module, data)
}

export function logInfo(message: string, module?: string, data?: any) {
  logger.info(message, module, data)
}

export function logWarn(message: string, module?: string, data?: any) {
  logger.warn(message, module, data)
}

export function logError(message: string, module?: string, data?: any, error?: Error) {
  logger.error(message, module, data, error)
}

// 装饰器：自动记录函数执行时间
export function logExecutionTime(module?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    
    descriptor.value = function (...args: any[]) {
      const startTime = performance.now()
      const result = method.apply(this, args)
      
      if (result instanceof Promise) {
        return result.finally(() => {
          logPerformance(`${target.constructor.name}.${propertyName}`, startTime, module)
        })
      } else {
        logPerformance(`${target.constructor.name}.${propertyName}`, startTime, module)
        return result
      }
    }
    
    return descriptor
  }
}

// 错误边界组件辅助函数
export function createErrorBoundary(componentName: string) {
  return {
    errorCaptured(error: Error, instance: any, info: string) {
      logger.error(`组件错误边界捕获`, componentName, {
        error: error.message,
        info,
        componentName: instance?.$options?.name
      }, error)
      
      // 返回false阻止错误继续传播
      return false
    }
  }
}
