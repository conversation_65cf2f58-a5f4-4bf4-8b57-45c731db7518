/**
 * 测试工具函数
 */

import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import type { Component } from 'vue'
import type { GISData, RoadData, SafetyCheckResult } from '@/types'

/**
 * 创建测试用的 Pinia 实例
 */
export function createTestPinia() {
  return createPinia()
}

/**
 * 创建测试用的路由实例
 */
export function createTestRouter() {
  return createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', component: { template: '<div>Home</div>' } },
      { path: '/gis', component: { template: '<div>GIS</div>' } },
      { path: '/data', component: { template: '<div>Data</div>' } },
      { path: '/road', component: { template: '<div>Road</div>' } },
      { path: '/safety', component: { template: '<div>Safety</div>' } },
      { path: '/report', component: { template: '<div>Report</div>' } },
      { path: '/monitor', component: { template: '<div>Monitor</div>' } }
    ]
  })
}

/**
 * 挂载组件的辅助函数
 */
export function mountComponent(
  component: Component,
  options: {
    props?: Record<string, any>
    global?: {
      plugins?: any[]
      stubs?: Record<string, any>
      mocks?: Record<string, any>
    }
  } = {}
): VueWrapper {
  const pinia = createTestPinia()
  const router = createTestRouter()

  return mount(component, {
    global: {
      plugins: [pinia, router],
      stubs: {
        'router-link': true,
        'router-view': true,
        ...options.global?.stubs
      },
      mocks: {
        $t: (key: string) => key,
        ...options.global?.mocks
      },
      ...options.global
    },
    props: options.props
  })
}

/**
 * 创建模拟的 GIS 数据
 */
export function createMockGISData(overrides: Partial<GISData> = {}): GISData {
  return {
    id: 'test-gis-data-1',
    name: '测试GIS数据',
    type: 'terrain',
    coordinates: [
      [116.3974, 39.9093, 50],
      [116.3984, 39.9103, 55],
      [116.3994, 39.9113, 60]
    ],
    metadata: {
      source: 'test',
      format: 'geojson',
      crs: 'EPSG:4326'
    },
    visible: true,
    createTime: '2024-01-01T00:00:00.000Z',
    ...overrides
  }
}

/**
 * 创建模拟的道路数据
 */
export function createMockRoadData(overrides: Partial<RoadData> = {}): RoadData {
  return {
    id: 'test-road-1',
    name: '测试道路',
    type: 'road',
    centerline: {
      points: [
        { longitude: 116.3974, latitude: 39.9093, elevation: 50, station: 0 },
        { longitude: 116.3984, latitude: 39.9103, elevation: 55, station: 100 },
        { longitude: 116.3994, latitude: 39.9113, elevation: 60, station: 200 }
      ],
      totalLength: 200,
      horizontalCurves: [
        { station: 100, radius: 60, length: 50, direction: 'left' }
      ],
      verticalCurves: []
    },
    metadata: {
      width: 7,
      roadType: 'main',
      designSpeed: 40,
      maxGrade: 8,
      minRadius: 50
    },
    ...overrides
  }
}

/**
 * 创建模拟的安全检测结果
 */
export function createMockSafetyResults(count: number = 3): SafetyCheckResult[] {
  const results: SafetyCheckResult[] = []
  
  for (let i = 0; i < count; i++) {
    results.push({
      type: ['grade', 'radius', 'sight_distance'][i % 3] as any,
      status: ['pass', 'warning', 'fail'][i % 3] as any,
      value: 50 + i * 10,
      standard: 60,
      message: `测试检测结果 ${i + 1}`,
      location: {
        station: i * 100,
        coordinates: [116.3974 + i * 0.001, 39.9093 + i * 0.001, 50 + i * 5]
      }
    })
  }
  
  return results
}

/**
 * 等待异步操作完成
 */
export async function waitFor(
  condition: () => boolean | Promise<boolean>,
  timeout: number = 5000
): Promise<void> {
  const startTime = Date.now()
  
  while (Date.now() - startTime < timeout) {
    const result = await condition()
    if (result) {
      return
    }
    await new Promise(resolve => setTimeout(resolve, 50))
  }
  
  throw new Error(`Condition not met within ${timeout}ms`)
}

/**
 * 模拟用户交互
 */
export class UserInteraction {
  constructor(private wrapper: VueWrapper) {}

  /**
   * 点击元素
   */
  async click(selector: string): Promise<void> {
    const element = this.wrapper.find(selector)
    if (!element.exists()) {
      throw new Error(`Element not found: ${selector}`)
    }
    await element.trigger('click')
    await this.wrapper.vm.$nextTick()
  }

  /**
   * 输入文本
   */
  async type(selector: string, text: string): Promise<void> {
    const element = this.wrapper.find(selector)
    if (!element.exists()) {
      throw new Error(`Element not found: ${selector}`)
    }
    await element.setValue(text)
    await this.wrapper.vm.$nextTick()
  }

  /**
   * 选择选项
   */
  async select(selector: string, value: string): Promise<void> {
    const element = this.wrapper.find(selector)
    if (!element.exists()) {
      throw new Error(`Element not found: ${selector}`)
    }
    await element.setValue(value)
    await this.wrapper.vm.$nextTick()
  }

  /**
   * 等待元素出现
   */
  async waitForElement(selector: string, timeout: number = 5000): Promise<void> {
    await waitFor(() => this.wrapper.find(selector).exists(), timeout)
  }

  /**
   * 等待文本出现
   */
  async waitForText(text: string, timeout: number = 5000): Promise<void> {
    await waitFor(() => this.wrapper.text().includes(text), timeout)
  }
}

/**
 * 创建用户交互实例
 */
export function createUserInteraction(wrapper: VueWrapper): UserInteraction {
  return new UserInteraction(wrapper)
}

/**
 * 性能测试工具
 */
export class PerformanceTest {
  private startTime: number = 0
  private endTime: number = 0

  start(): void {
    this.startTime = performance.now()
  }

  end(): number {
    this.endTime = performance.now()
    return this.endTime - this.startTime
  }

  measure(fn: () => void | Promise<void>): Promise<number> {
    return new Promise(async (resolve) => {
      this.start()
      await fn()
      resolve(this.end())
    })
  }
}

/**
 * 创建性能测试实例
 */
export function createPerformanceTest(): PerformanceTest {
  return new PerformanceTest()
}

/**
 * 内存使用监控
 */
export function getMemoryUsage(): {
  used: number
  total: number
  percentage: number
} {
  if (typeof performance !== 'undefined' && (performance as any).memory) {
    const memory = (performance as any).memory
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
    }
  }
  
  return {
    used: 0,
    total: 0,
    percentage: 0
  }
}

/**
 * 模拟网络延迟
 */
export function mockNetworkDelay(delay: number = 100): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, delay))
}

/**
 * 生成随机测试数据
 */
export function generateRandomData(type: 'coordinates' | 'elevation' | 'station', count: number = 10): number[] {
  const data: number[] = []
  
  for (let i = 0; i < count; i++) {
    switch (type) {
      case 'coordinates':
        data.push(116.3974 + Math.random() * 0.01)
        break
      case 'elevation':
        data.push(50 + Math.random() * 100)
        break
      case 'station':
        data.push(i * 100)
        break
    }
  }
  
  return data
}
